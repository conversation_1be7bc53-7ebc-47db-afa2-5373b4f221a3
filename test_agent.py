"""
测试留学申请Agent的基本功能
"""
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from models import UserProfile, InstitutionType, Country
from data_loader import data_loader


def test_data_loading():
    """测试数据加载"""
    print("🔍 测试数据加载...")
    
    # 测试大学数据
    universities = data_loader.universities_df
    print(f"✅ 大学数据: {len(universities)} 所大学")
    print(f"   前5所大学: {universities['university_name_cn'].head().tolist()}")
    
    # 测试专业数据
    programs = data_loader.programs_df
    print(f"✅ 专业数据: {len(programs)} 个专业")
    
    # 测试申请历史数据
    applications = data_loader.applications_df
    print(f"✅ 申请历史: {len(applications)} 条记录")
    
    return True


def test_user_profile():
    """测试用户档案"""
    print("\n👤 测试用户档案...")
    
    # 创建测试用户档案
    profile = UserProfile(
        name="张三",
        age=22,
        undergraduate_school="北京大学",
        undergraduate_major="计算机科学与技术",
        institution_type=InstitutionType.C9,
        gpa=3.6,
        degree_year=2024,
        toefl_score=105,
        target_countries=[Country.USA, Country.UK],
        target_majors=["Computer Science", "Artificial Intelligence"]
    )
    
    print(f"✅ 用户档案创建成功")
    print(f"   姓名: {profile.name}")
    print(f"   院校: {profile.undergraduate_school}")
    print(f"   专业: {profile.undergraduate_major}")
    print(f"   GPA: {profile.gpa}")
    print(f"   目标国家: {[c.value for c in profile.target_countries]}")
    
    return profile


def test_recommendation_logic():
    """测试推荐逻辑（不使用LLM）"""
    print("\n🎯 测试推荐逻辑...")
    
    # 获取美国和英国的大学
    target_countries = ["United States", "United Kingdom"]
    universities = data_loader.get_universities_by_country(target_countries)
    print(f"✅ 筛选出 {len(universities)} 所目标国家大学")
    
    # 获取计算机相关专业
    cs_keywords = ["计算机", "人工智能", "数据科学", "软件"]
    programs = data_loader.get_programs_by_major_keywords(cs_keywords)
    print(f"✅ 筛选出 {len(programs)} 个相关专业")
    
    # 合并数据
    merged = programs.merge(universities, on='university_id', how='inner')
    print(f"✅ 合并后得到 {len(merged)} 个候选项目")
    
    if len(merged) > 0:
        # 显示前5个候选项目
        print("   前5个候选项目:")
        for i, (_, row) in enumerate(merged.head().iterrows(), 1):
            print(f"   {i}. {row['university_name_cn']} - {row['program_name']}")
    
    return len(merged) > 0


def test_similar_applications():
    """测试相似申请案例查找"""
    print("\n📊 测试相似申请案例...")
    
    # 测试用户背景
    user_background = {
        'gpa': 3.6,
        'major': '计算机科学',
        'institution_type': '985'
    }
    
    similar_apps = data_loader.get_similar_applications(user_background, top_k=5)
    print(f"✅ 找到 {len(similar_apps)} 个相似申请案例")
    
    if len(similar_apps) > 0:
        print("   相似案例:")
        for i, (_, app) in enumerate(similar_apps.head().iterrows(), 1):
            print(f"   {i}. {app['institution_name']} - {app['major']} - GPA:{app['gpa']} - 结果:{app['admission_result']}")
    
    return len(similar_apps) > 0


def test_course_data():
    """测试课程数据"""
    print("\n📚 测试课程数据...")
    
    # 获取第一个专业的课程
    first_program_id = data_loader.programs_df.iloc[0]['program_id']
    courses = data_loader.get_program_courses(str(first_program_id))
    
    print(f"✅ 专业 {first_program_id} 有 {len(courses)} 门课程")
    
    if courses:
        print("   课程示例:")
        for i, course in enumerate(courses[:3], 1):
            print(f"   {i}. {course['course_name']} ({course['course_type']})")
    
    return True


def main():
    """主测试函数"""
    print("🎓 留学申请Agent 功能测试")
    print("=" * 50)
    
    try:
        # 测试数据加载
        if not test_data_loading():
            print("❌ 数据加载测试失败")
            return False
        
        # 测试用户档案
        profile = test_user_profile()
        if not profile:
            print("❌ 用户档案测试失败")
            return False
        
        # 测试推荐逻辑
        if not test_recommendation_logic():
            print("❌ 推荐逻辑测试失败")
            return False
        
        # 测试相似申请案例
        if not test_similar_applications():
            print("❌ 相似申请案例测试失败")
            return False
        
        # 测试课程数据
        if not test_course_data():
            print("❌ 课程数据测试失败")
            return False
        
        print("\n🎉 所有基础功能测试通过！")
        print("\n📝 下一步:")
        print("1. 设置 OPENAI_API_KEY 环境变量")
        print("2. 运行 python run.py 启动完整应用")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
