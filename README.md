# 🎓 留学申请匹配Agent

基于LangGraph的智能留学申请助手，帮助国际学生匹配最适合的研究生项目并生成个性化申请文书。

## ✨ 功能特色

### 🔍 智能背景分析
- 📊 **成绩单OCR识别**: 自动识别成绩单图片，提取课程和分数
- 🧮 **GPA智能计算**: 支持百分制、五分制转四分制GPA
- ✅ **人工确认修正**: 用户可确认和修正识别结果

### 🎯 精准匹配推荐
- 🌍 **多国家支持**: 美加英澳新、日韩、欧洲等主要留学目的地
- 🏫 **20所精选推荐**: 基于科学评分算法推荐最匹配的大学
- 📊 **三档分类**: 冲刺院校(4所) + 匹配院校(10所) + 保底院校(6所)
- 🔢 **综合评分**: 考虑GPA、院校背景、专业匹配度、语言成绩等多维度

### 📝 个性化文书生成
- 📄 **多类型文书**: Personal Statement、Statement of Purpose、Why School等
- 🎨 **定制化内容**: 根据用户背景和目标学校特色生成
- ✏️ **在线编辑**: 支持文书在线编辑和下载

### 💬 智能对话交互
- 🤖 **聊天式界面**: 类似ChatGPT的对话体验
- 🔄 **多轮交互**: 支持信息补充和修正
- 📱 **响应式设计**: 适配不同设备屏幕

## 🏗️ 技术架构

### 核心技术栈
- **LangGraph**: Agent工作流编排
- **LangChain**: LLM集成和RAG
- **Streamlit**: Web界面
- **OpenAI GPT-4**: 主要语言模型
- **FAISS**: 向量检索
- **OpenCV + Tesseract**: OCR识别

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit     │    │   LangGraph     │    │   Data Layer    │
│   Web界面       │◄──►│   Agent工作流   │◄──►│   大学数据库    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户交互      │    │   推荐引擎      │    │   申请历史      │
│   状态管理      │    │   文书生成      │    │   QS排名数据    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Agent工作流
```
开始 → 收集基本信息 → 处理成绩单 → 收集偏好 → 生成推荐 → 选择项目 → 处理简历 → 生成文书 → 完成
```

## 📊 数据基础

### 大学数据 (100所QS排名大学)
- 🏫 **universities.csv**: QS前100大学信息
- 🏛️ **college.csv**: 1,012个学院
- 🎓 **department.csv**: 6,403个系
- 📚 **degree_programs.csv**: 13,902个硕士专业

### 课程数据
- 📖 **courses.csv**: 580门课程
- 📋 **course_selection.csv**: 62,617条选课记录

### 申请历史
- 📈 **graduate_application.csv**: 1,400+真实申请案例

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd study-abroad-agent

# 安装Python依赖
pip install -r requirements.txt
```

### 2. 配置API密钥
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，设置OpenAI API密钥
OPENAI_API_KEY=your_openai_api_key_here
```

### 3. 启动应用
```bash
# 使用启动脚本（推荐）
python run.py

# 或直接启动Streamlit
streamlit run app.py
```

### 4. 访问应用
打开浏览器访问: http://localhost:8501

## 📖 使用指南

### 第一步：基本信息
- 输入姓名、年龄、本科院校等基本信息
- 选择院校类型（C9/985/211/双一流/普通本科等）

### 第二步：成绩分析
- 上传成绩单图片（支持OCR识别）
- 或直接输入GPA信息
- 确认和修正识别结果

### 第三步：偏好设置
- 选择目标国家/地区
- 选择目标专业领域
- 设置入学时间偏好

### 第四步：查看推荐
- 获得20所大学的个性化推荐
- 查看详细的匹配分析和推荐理由
- 了解申请要求和截止日期

### 第五步：选择目标
- 从推荐中选择心仪的学校（最多10所）
- 系统将为每所学校生成定制文书

### 第六步：简历分析
- 上传简历或描述个人经历
- 系统分析研究经验、实习经历等

### 第七步：文书生成
- 获得个性化申请文书
- 支持在线编辑和下载
- 针对不同学校的定制化内容

## 🎯 推荐算法

### 评分体系 (总分100分)
- **GPA分数 (30%)**: 考虑院校权重的GPA匹配度
- **院校声誉 (10%)**: 本科院校国际声誉和QS排名
- **专业相关度 (15%)**: 使用TF-IDF计算专业相似度
- **语言成绩 (10%)**: TOEFL/IELTS与要求的匹配度
- **标准化考试 (10%)**: GRE/GMAT成绩评估
- **研究经历 (10%)**: 论文发表、研究项目经验
- **课外活动 (5%)**: 获奖情况、专利等
- **实习经历 (5%)**: 知名企业实习、工作经验
- **推荐信 (5%)**: 推荐人背景和声誉

### 匹配类型
- **冲刺院校**: 匹配度85%+，用户条件接近录取下限
- **匹配院校**: 匹配度70-84%，用户条件位于录取中位数
- **保底院校**: 匹配度<70%，用户条件明显高于历史录取

## 🔧 自定义配置

### 修改推荐数量
在 `src/recommendation_engine.py` 中调整 `top_k` 参数

### 添加新的评分因子
在 `RecommendationEngine.calculate_match_score()` 方法中添加新的评分逻辑

### 自定义文书模板
在 `src/document_generator.py` 中修改文书模板

## 📁 项目结构
```
study-abroad-agent/
├── src/                    # 核心代码
│   ├── models.py          # 数据模型
│   ├── agent_graph.py     # LangGraph工作流
│   ├── recommendation_engine.py  # 推荐引擎
│   ├── document_generator.py     # 文书生成
│   ├── ocr_processor.py   # OCR处理
│   └── data_loader.py     # 数据加载
├── app.py                 # Streamlit应用
├── run.py                 # 启动脚本
├── requirements.txt       # Python依赖
├── .env.example          # 环境变量模板
├── *.csv                 # 数据文件
└── README.md             # 项目文档
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 开发环境设置
```bash
# 安装开发依赖
pip install -r requirements.txt

# 运行测试
python -m pytest tests/

# 代码格式化
black src/ app.py
```

## 📄 许可证

MIT License

## 🙏 致谢

- OpenAI GPT-4 提供强大的语言理解能力
- QS世界大学排名提供权威的大学数据
- LangChain/LangGraph 提供优秀的Agent框架
- Streamlit 提供简洁的Web界面框架

---

💡 **提示**: 这是一个AI驱动的留学申请助手，建议结果仅供参考，请结合实际情况和专业顾问意见做出最终决策。
