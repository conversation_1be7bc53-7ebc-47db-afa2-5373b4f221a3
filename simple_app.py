"""
简化版留学申请匹配Agent - Streamlit应用
不依赖LangGraph和复杂的LLM集成
"""
import streamlit as st
import pandas as pd
from typing import Dict, Any, List
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from models import UserProfile, InstitutionType, Country
from simple_recommendation import SimpleRecommendationEngine
from simple_document_generator import SimpleDocumentGenerator
from data_loader import data_loader

# 页面配置
st.set_page_config(
    page_title="留学申请匹配Agent",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .recommendation-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        background-color: #fafafa;
    }
    .match-score {
        font-weight: bold;
        color: #1f77b4;
    }
    .match-type-冲刺 {
        color: #ff5722;
        font-weight: bold;
    }
    .match-type-匹配 {
        color: #4caf50;
        font-weight: bold;
    }
    .match-type-保底 {
        color: #2196f3;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """初始化会话状态"""
    if "user_profile" not in st.session_state:
        st.session_state.user_profile = UserProfile()
    
    if "recommendations" not in st.session_state:
        st.session_state.recommendations = []
    
    if "selected_programs" not in st.session_state:
        st.session_state.selected_programs = []
    
    if "documents" not in st.session_state:
        st.session_state.documents = []
    
    if "current_step" not in st.session_state:
        st.session_state.current_step = "basic_info"


def collect_basic_info():
    """收集基本信息"""
    st.markdown("### 📝 基本信息")
    
    col1, col2 = st.columns(2)
    
    with col1:
        name = st.text_input("姓名", value=st.session_state.user_profile.name or "")
        age = st.number_input("年龄", min_value=18, max_value=35, value=st.session_state.user_profile.age or 22)
        school = st.text_input("本科院校", value=st.session_state.user_profile.undergraduate_school or "")
        major = st.text_input("本科专业", value=st.session_state.user_profile.undergraduate_major or "")
    
    with col2:
        institution_type = st.selectbox(
            "院校类型",
            options=[e.value for e in InstitutionType],
            index=0 if not st.session_state.user_profile.institution_type else 
                  list(InstitutionType).index(st.session_state.user_profile.institution_type)
        )
        
        gpa = st.number_input("GPA", min_value=0.0, max_value=4.0, value=st.session_state.user_profile.gpa or 3.0, step=0.1)
        degree_year = st.number_input("毕业年份", min_value=2020, max_value=2030, value=st.session_state.user_profile.degree_year or 2024)
    
    # 语言成绩
    st.markdown("#### 🗣️ 语言成绩")
    col3, col4 = st.columns(2)
    
    with col3:
        toefl = st.number_input("TOEFL分数", min_value=0, max_value=120, value=st.session_state.user_profile.toefl_score or 0)
    
    with col4:
        ielts = st.number_input("IELTS分数", min_value=0.0, max_value=9.0, value=st.session_state.user_profile.ielts_score or 0.0, step=0.5)
    
    # 更新用户档案
    st.session_state.user_profile.name = name
    st.session_state.user_profile.age = age
    st.session_state.user_profile.undergraduate_school = school
    st.session_state.user_profile.undergraduate_major = major
    st.session_state.user_profile.institution_type = InstitutionType(institution_type)
    st.session_state.user_profile.gpa = gpa
    st.session_state.user_profile.degree_year = degree_year
    st.session_state.user_profile.toefl_score = toefl if toefl > 0 else None
    st.session_state.user_profile.ielts_score = ielts if ielts > 0 else None


def collect_preferences():
    """收集留学偏好"""
    st.markdown("### 🎯 留学偏好")
    
    # 目标国家
    countries = st.multiselect(
        "目标国家/地区",
        options=[c.value for c in Country],
        default=[c.value for c in st.session_state.user_profile.target_countries] if st.session_state.user_profile.target_countries else []
    )
    
    # 目标专业
    majors = st.multiselect(
        "目标专业领域",
        options=[
            "Computer Science", "Artificial Intelligence", "Data Science",
            "Engineering", "Business Administration", "Economics",
            "Mathematics", "Physics", "Chemistry", "Biology"
        ],
        default=st.session_state.user_profile.target_majors or []
    )
    
    # 入学时间
    term = st.selectbox(
        "希望入学时间",
        options=["2025 Fall", "2025 Spring", "2026 Fall", "2026 Spring"],
        index=0 if not st.session_state.user_profile.preferred_term else 
              ["2025 Fall", "2025 Spring", "2026 Fall", "2026 Spring"].index(st.session_state.user_profile.preferred_term)
    )
    
    # 更新用户档案
    st.session_state.user_profile.target_countries = [Country(c) for c in countries]
    st.session_state.user_profile.target_majors = majors
    st.session_state.user_profile.preferred_term = term


def generate_recommendations():
    """生成推荐"""
    if st.button("🎯 生成推荐", type="primary"):
        with st.spinner("正在分析您的背景并生成推荐..."):
            engine = SimpleRecommendationEngine()
            recommendations = engine.get_recommendations(st.session_state.user_profile, top_k=20)
            st.session_state.recommendations = recommendations
            st.session_state.current_step = "view_recommendations"
            st.rerun()


def display_recommendations():
    """显示推荐结果"""
    if not st.session_state.recommendations:
        st.info("请先生成推荐")
        return
    
    st.markdown("### 🎓 推荐结果")
    
    # 按匹配类型分组
    reach_schools = [r for r in st.session_state.recommendations if r.match_type == "冲刺"]
    match_schools = [r for r in st.session_state.recommendations if r.match_type == "匹配"]
    safety_schools = [r for r in st.session_state.recommendations if r.match_type == "保底"]
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["🚀 冲刺院校", "🎯 匹配院校", "🛡️ 保底院校", "📊 数据表格"])
    
    with tab1:
        if reach_schools:
            for i, rec in enumerate(reach_schools, 1):
                display_recommendation_card(i, rec)
        else:
            st.info("暂无冲刺院校推荐")
    
    with tab2:
        if match_schools:
            for i, rec in enumerate(match_schools, len(reach_schools) + 1):
                display_recommendation_card(i, rec)
        else:
            st.info("暂无匹配院校推荐")
    
    with tab3:
        if safety_schools:
            for i, rec in enumerate(safety_schools, len(reach_schools) + len(match_schools) + 1):
                display_recommendation_card(i, rec)
        else:
            st.info("暂无保底院校推荐")
    
    with tab4:
        # 创建数据表格
        df_data = []
        for i, rec in enumerate(st.session_state.recommendations, 1):
            df_data.append({
                "编号": i,
                "大学": rec.program.university_name_cn,
                "专业": rec.program.program_name,
                "QS排名": rec.program.qs_ranking,
                "学制": rec.program.duration,
                "学费": rec.program.tuition_fee,
                "GPA要求": rec.program.gpa_requirement,
                "语言要求": rec.program.language_requirement,
                "申请截止": rec.program.application_deadline,
                "匹配度": f"{rec.match_score:.1f}%",
                "类型": rec.match_type
            })
        
        df = pd.DataFrame(df_data)
        st.dataframe(df, use_container_width=True)


def display_recommendation_card(index: int, rec):
    """显示单个推荐卡片"""
    match_type_class = f"match-type-{rec.match_type}"
    
    st.markdown(f"""
    <div class="recommendation-card">
        <h4>{index}. {rec.program.university_name_cn} 
            <span class="match-score">({rec.match_score:.1f}%)</span>
            <span class="{match_type_class}">[{rec.match_type}]</span>
        </h4>
        <p><strong>🏫 QS排名:</strong> #{rec.program.qs_ranking}</p>
        <p><strong>📚 专业:</strong> {rec.program.program_name}</p>
        <p><strong>⏱️ 学制:</strong> {rec.program.duration}</p>
        <p><strong>💰 学费:</strong> {rec.program.tuition_fee}</p>
        <p><strong>📊 GPA要求:</strong> {rec.program.gpa_requirement}</p>
        <p><strong>🗣️ 语言要求:</strong> {rec.program.language_requirement}</p>
        <p><strong>📅 申请截止:</strong> {rec.program.application_deadline}</p>
        <p><strong>💡 推荐理由:</strong> {rec.recommendation_reason}</p>
        <p><strong>🔗 官网:</strong> <a href="{rec.program.official_website}" target="_blank">查看详情</a></p>
    </div>
    """, unsafe_allow_html=True)


def select_programs():
    """选择项目"""
    st.markdown("### 🎯 选择目标项目")

    # 检查是否有推荐结果
    if not st.session_state.recommendations:
        st.warning("⚠️ 没有找到推荐结果，请先生成推荐。")
        if st.button("返回生成推荐"):
            st.session_state.current_step = "generate_recs"
            st.rerun()
        return

    st.write(f"请从以下 {len(st.session_state.recommendations)} 个推荐中选择您感兴趣的项目（最多10个）：")

    # 显示推荐统计
    reach_count = len([r for r in st.session_state.recommendations if r.match_type == "冲刺"])
    match_count = len([r for r in st.session_state.recommendations if r.match_type == "匹配"])
    safety_count = len([r for r in st.session_state.recommendations if r.match_type == "保底"])

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("🚀 冲刺院校", reach_count)
    with col2:
        st.metric("🎯 匹配院校", match_count)
    with col3:
        st.metric("🛡️ 保底院校", safety_count)

    st.markdown("---")

    selected_indices = []

    # 按类型分组显示
    reach_schools = [r for r in st.session_state.recommendations if r.match_type == "冲刺"]
    match_schools = [r for r in st.session_state.recommendations if r.match_type == "匹配"]
    safety_schools = [r for r in st.session_state.recommendations if r.match_type == "保底"]

    # 冲刺院校
    if reach_schools:
        st.markdown("#### 🚀 冲刺院校")
        for i, rec in enumerate(reach_schools):
            original_index = st.session_state.recommendations.index(rec)
            if st.checkbox(
                f"**{rec.program.university_name_cn}** - {rec.program.program_name} (匹配度: {rec.match_score:.1f}%)",
                key=f"select_reach_{original_index}"
            ):
                selected_indices.append(original_index)

    # 匹配院校
    if match_schools:
        st.markdown("#### 🎯 匹配院校")
        for i, rec in enumerate(match_schools):
            original_index = st.session_state.recommendations.index(rec)
            if st.checkbox(
                f"**{rec.program.university_name_cn}** - {rec.program.program_name} (匹配度: {rec.match_score:.1f}%)",
                key=f"select_match_{original_index}"
            ):
                selected_indices.append(original_index)

    # 保底院校
    if safety_schools:
        st.markdown("#### 🛡️ 保底院校")
        for i, rec in enumerate(safety_schools):
            original_index = st.session_state.recommendations.index(rec)
            if st.checkbox(
                f"**{rec.program.university_name_cn}** - {rec.program.program_name} (匹配度: {rec.match_score:.1f}%)",
                key=f"select_safety_{original_index}"
            ):
                selected_indices.append(original_index)

    # 显示选择统计
    if selected_indices:
        st.info(f"✅ 已选择 {len(selected_indices)} 个项目")

    # 确认选择按钮
    col1, col2 = st.columns([1, 1])
    with col1:
        if st.button("确认选择", type="primary", disabled=len(selected_indices) == 0):
            st.session_state.selected_programs = [st.session_state.recommendations[i].program for i in selected_indices[:10]]
            st.session_state.current_step = "generate_documents"
            st.success(f"已选择 {len(st.session_state.selected_programs)} 个项目")
            st.rerun()

    with col2:
        if st.button("返回查看推荐"):
            st.session_state.current_step = "view_recommendations"
            st.rerun()


def generate_documents():
    """生成申请文书"""
    if not st.session_state.selected_programs:
        st.warning("请先选择目标项目")
        return
    
    st.markdown("### 📝 生成申请文书")
    
    # 简历信息
    resume_text = st.text_area(
        "请简要描述您的研究经历、实习经历、获奖情况等：",
        height=150,
        placeholder="例如：参与过机器学习项目研究，在腾讯实习过，获得过数学建模竞赛奖项等..."
    )
    
    if st.button("生成文书", type="primary"):
        with st.spinner("正在为您生成个性化申请文书..."):
            generator = SimpleDocumentGenerator()
            documents = []
            
            for program in st.session_state.selected_programs:
                doc = generator.generate_personal_statement(
                    st.session_state.user_profile, 
                    program, 
                    resume_text
                )
                documents.append(doc)
            
            st.session_state.documents = documents
            st.success(f"已为 {len(documents)} 个项目生成申请文书！")


def display_documents():
    """显示申请文书"""
    if not st.session_state.documents:
        return
    
    st.markdown("### 📄 申请文书")
    
    for i, doc in enumerate(st.session_state.documents):
        with st.expander(f"{doc.target_program.university_name_cn} - {doc.document_type}"):
            st.markdown(f"**目标项目:** {doc.target_program.program_name}")
            st.markdown("**文书内容:**")
            
            # 可编辑的文书内容
            edited_content = st.text_area(
                "内容",
                value=doc.content,
                height=400,
                key=f"doc_{i}",
                help="您可以编辑和完善这份文书"
            )
            
            # 更新文书内容
            doc.content = edited_content
            
            # 下载按钮
            st.download_button(
                label="📥 下载文书",
                data=doc.content,
                file_name=f"{doc.target_program.university_name_cn}_{doc.document_type}.txt",
                mime="text/plain"
            )


def main():
    """主函数"""
    initialize_session_state()
    
    # 页面标题
    st.markdown('<h1 class="main-header">🎓 留学申请匹配Agent</h1>', unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.markdown("### 📊 数据统计")
        st.metric("大学数量", len(data_loader.universities_df))
        st.metric("专业数量", len(data_loader.programs_df))
        st.metric("申请案例", len(data_loader.applications_df))
        
        st.markdown("### 📋 进度")
        steps = [
            ("basic_info", "基本信息"),
            ("preferences", "留学偏好"),
            ("generate_recs", "生成推荐"),
            ("view_recommendations", "查看推荐"),
            ("select_programs", "选择项目"),
            ("generate_documents", "生成文书"),
            ("completed", "完成")
        ]
        
        current_step = st.session_state.current_step
        for step_id, step_name in steps:
            if step_id == current_step:
                st.markdown(f"🔄 **{step_name}** (当前)")
            elif steps.index((step_id, step_name)) < steps.index((current_step, next(name for sid, name in steps if sid == current_step))):
                st.markdown(f"✅ {step_name}")
            else:
                st.markdown(f"⏳ {step_name}")
        
        if st.button("🔄 重新开始"):
            for key in list(st.session_state.keys()):
                del st.session_state[key]
            st.rerun()
    
    # 主要内容
    if st.session_state.current_step == "basic_info":
        collect_basic_info()
        if st.button("下一步：设置偏好"):
            st.session_state.current_step = "preferences"
            st.rerun()
    
    elif st.session_state.current_step == "preferences":
        collect_preferences()
        if st.button("下一步：生成推荐"):
            st.session_state.current_step = "generate_recs"
            st.rerun()
    
    elif st.session_state.current_step == "generate_recs":
        st.markdown("### 🚀 准备生成推荐")
        st.write("基于您的背景信息，我们将为您推荐最适合的20所大学和专业。")
        generate_recommendations()
    
    elif st.session_state.current_step == "view_recommendations":
        display_recommendations()
        if st.button("下一步：选择项目"):
            st.session_state.current_step = "select_programs"
            st.rerun()
    
    elif st.session_state.current_step == "select_programs":
        select_programs()
    
    elif st.session_state.current_step == "generate_documents":
        generate_documents()
        display_documents()
    
    # 页脚
    st.markdown("---")
    st.markdown(
        "💡 **提示**: 这是一个AI驱动的留学申请助手，建议结果仅供参考，请结合实际情况做出决策。"
    )


if __name__ == "__main__":
    main()
