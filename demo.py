"""
留学申请匹配Agent演示脚本
"""
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from models import UserProfile, InstitutionType, Country
from simple_recommendation import SimpleRecommendationEngine
from simple_document_generator import SimpleDocumentGenerator
from data_loader import data_loader


def print_separator(title=""):
    """打印分隔线"""
    print("=" * 60)
    if title:
        print(f" {title} ".center(60, "="))
        print("=" * 60)


def demo_user_profile():
    """演示用户档案创建"""
    print_separator("用户背景信息")
    
    profile = UserProfile(
        name="李明",
        age=23,
        undergraduate_school="清华大学",
        undergraduate_major="计算机科学与技术",
        institution_type=InstitutionType.C9,
        gpa=3.7,
        degree_year=2024,
        toefl_score=108,
        target_countries=[Country.USA, Country.UK, Country.CANADA],
        target_majors=["计算机科学", "人工智能", "数据科学"]
    )
    
    print(f"👤 姓名: {profile.name}")
    print(f"🎓 本科院校: {profile.undergraduate_school}")
    print(f"📚 本科专业: {profile.undergraduate_major}")
    print(f"🏛️ 院校类型: {profile.institution_type.value}")
    print(f"📊 GPA: {profile.gpa}")
    print(f"🗣️ TOEFL: {profile.toefl_score}")
    print(f"🌍 目标国家: {', '.join([c.value for c in profile.target_countries])}")
    print(f"🎯 目标专业: {', '.join(profile.target_majors)}")
    
    return profile


def demo_recommendations(profile):
    """演示推荐功能"""
    print_separator("智能推荐结果")
    
    engine = SimpleRecommendationEngine()
    recommendations = engine.get_recommendations(profile, top_k=15)
    
    print(f"🎯 为您推荐 {len(recommendations)} 所大学和专业：\n")
    
    # 按匹配类型分组
    reach_schools = [r for r in recommendations if r.match_type == "冲刺"]
    match_schools = [r for r in recommendations if r.match_type == "匹配"]
    safety_schools = [r for r in recommendations if r.match_type == "保底"]
    
    if reach_schools:
        print("🚀 冲刺院校:")
        for i, rec in enumerate(reach_schools, 1):
            print(f"  {i}. {rec.program.university_name_cn} - {rec.program.program_name}")
            print(f"     QS排名: #{rec.program.qs_ranking} | 匹配度: {rec.match_score:.1f}%")
            print(f"     学费: {rec.program.tuition_fee} | GPA要求: {rec.program.gpa_requirement}")
            print(f"     推荐理由: {rec.recommendation_reason}")
            print()
    
    if match_schools:
        print("🎯 匹配院校:")
        for i, rec in enumerate(match_schools, len(reach_schools) + 1):
            print(f"  {i}. {rec.program.university_name_cn} - {rec.program.program_name}")
            print(f"     QS排名: #{rec.program.qs_ranking} | 匹配度: {rec.match_score:.1f}%")
            print(f"     学费: {rec.program.tuition_fee} | GPA要求: {rec.program.gpa_requirement}")
            print(f"     推荐理由: {rec.recommendation_reason}")
            print()
    
    if safety_schools:
        print("🛡️ 保底院校:")
        for i, rec in enumerate(safety_schools, len(reach_schools) + len(match_schools) + 1):
            print(f"  {i}. {rec.program.university_name_cn} - {rec.program.program_name}")
            print(f"     QS排名: #{rec.program.qs_ranking} | 匹配度: {rec.match_score:.1f}%")
            print(f"     学费: {rec.program.tuition_fee} | GPA要求: {rec.program.gpa_requirement}")
            print(f"     推荐理由: {rec.recommendation_reason}")
            print()
    
    return recommendations


def demo_document_generation(profile, recommendations):
    """演示文书生成功能"""
    print_separator("申请文书生成")
    
    if not recommendations:
        print("❌ 没有推荐结果，无法生成文书")
        return
    
    # 选择前3个推荐生成文书
    selected_programs = [rec.program for rec in recommendations[:3]]
    
    generator = SimpleDocumentGenerator()
    
    print(f"📝 为前3个推荐项目生成申请文书：\n")
    
    for i, program in enumerate(selected_programs, 1):
        print(f"📄 {i}. {program.university_name_cn} - {program.program_name}")
        print("-" * 50)
        
        # 生成个人陈述
        resume_text = """
        研究经历：参与过深度学习项目，发表过1篇会议论文
        实习经历：在字节跳动实习3个月，负责推荐算法优化
        获奖情况：ACM程序设计竞赛省级二等奖，国家奖学金
        """
        
        doc = generator.generate_personal_statement(profile, program, resume_text)
        
        # 显示文书摘要（前200字符）
        content_preview = doc.content[:200] + "..." if len(doc.content) > 200 else doc.content
        print(content_preview)
        print()


def demo_data_statistics():
    """演示数据统计"""
    print_separator("数据库统计信息")
    
    universities = data_loader.universities_df
    programs = data_loader.programs_df
    applications = data_loader.applications_df
    
    print(f"🏫 大学数量: {len(universities)} 所")
    print(f"📚 专业数量: {len(programs)} 个")
    print(f"📊 申请历史: {len(applications)} 条记录")
    print()
    
    # 按国家统计大学数量
    country_stats = universities['country'].value_counts().head(5)
    print("🌍 主要留学国家:")
    for country, count in country_stats.items():
        print(f"   {country}: {count} 所大学")
    print()
    
    # 按专业统计
    major_stats = programs['program_name'].value_counts().head(5)
    print("🎓 热门专业:")
    for major, count in major_stats.items():
        print(f"   {major}: {count} 个项目")


def main():
    """主演示函数"""
    print("🎓 留学申请匹配Agent 演示")
    print("基于LangGraph的智能留学申请助手")
    print()
    
    try:
        # 1. 数据统计
        demo_data_statistics()
        
        # 2. 用户档案
        profile = demo_user_profile()
        
        # 3. 智能推荐
        recommendations = demo_recommendations(profile)
        
        # 4. 文书生成
        demo_document_generation(profile, recommendations)
        
        print_separator("演示完成")
        print("✅ 所有功能演示完成！")
        print()
        print("🚀 下一步:")
        print("1. 安装 streamlit: pip install streamlit")
        print("2. 运行完整应用: streamlit run simple_app.py")
        print("3. 或设置 OpenAI API 运行完整版: python run.py")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
