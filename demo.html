<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎓 留学申请匹配Agent - 演示界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1f77b4 0%, #2e86de 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 30px;
            border-radius: 15px;
            background: #f8f9fa;
            border-left: 5px solid #1f77b4;
        }
        
        .demo-section h2 {
            color: #1f77b4;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #1f77b4;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .user-profile {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .profile-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .profile-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            width: 30px;
        }
        
        .recommendation-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #4caf50;
        }
        
        .recommendation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .university-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: #1f77b4;
        }
        
        .match-score {
            background: #4caf50;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .program-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
        }
        
        .detail-label {
            font-weight: bold;
            margin-right: 10px;
            color: #666;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #1f77b4 0%, #2e86de 100%);
            color: white;
            padding: 40px;
            text-align: center;
            border-radius: 15px;
            margin-top: 40px;
        }
        
        .cta-button {
            background: white;
            color: #1f77b4;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.3s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 留学申请匹配Agent</h1>
            <p>基于LangGraph的智能留学申请助手</p>
        </div>
        
        <div class="content">
            <!-- 数据统计 -->
            <div class="demo-section">
                <h2>📊 数据库统计</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">100</div>
                        <div class="stat-label">所大学</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">13,902</div>
                        <div class="stat-label">个专业</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2,000</div>
                        <div class="stat-label">条申请历史</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">9</div>
                        <div class="stat-label">个国家地区</div>
                    </div>
                </div>
            </div>
            
            <!-- 用户背景 -->
            <div class="demo-section">
                <h2>👤 用户背景信息</h2>
                <div class="user-profile">
                    <div class="profile-item">
                        <span class="profile-icon">👤</span>
                        <span><strong>姓名:</strong> 李明</span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-icon">🎓</span>
                        <span><strong>本科院校:</strong> 清华大学 (C9)</span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-icon">📚</span>
                        <span><strong>本科专业:</strong> 计算机科学与技术</span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-icon">📊</span>
                        <span><strong>GPA:</strong> 3.7/4.0</span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-icon">🗣️</span>
                        <span><strong>TOEFL:</strong> 108</span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-icon">🌍</span>
                        <span><strong>目标国家:</strong> 美国, 英国, 加拿大</span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-icon">🎯</span>
                        <span><strong>目标专业:</strong> 计算机科学, 人工智能, 数据科学</span>
                    </div>
                </div>
            </div>
            
            <!-- 推荐结果 -->
            <div class="demo-section">
                <h2>🎯 智能推荐结果</h2>
                
                <div class="recommendation-card">
                    <div class="recommendation-header">
                        <div class="university-name">麻省理工学院 (MIT)</div>
                        <div class="match-score">64.0%</div>
                    </div>
                    <div><strong>专业:</strong> 计算机科学与工程（双语班）</div>
                    <div class="program-details">
                        <div class="detail-item">
                            <span class="detail-label">🏫 QS排名:</span>
                            <span>#1</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">💰 学费:</span>
                            <span>$60,000</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">📊 GPA要求:</span>
                            <span>3.4</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">⏱️ 学制:</span>
                            <span>2年</span>
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <strong>💡 推荐理由:</strong> 您的GPA 3.7 满足要求；QS世界排名第1位，声誉卓著；专业高度匹配
                    </div>
                </div>
                
                <div style="text-align: center; color: #666; margin: 20px 0;">
                    ... 还有5所推荐大学 ...
                </div>
            </div>
            
            <!-- 核心功能 -->
            <div class="demo-section">
                <h2>✨ 核心功能</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3>智能匹配</h3>
                        <p>基于多维度评分算法，精准匹配最适合的大学和专业</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3>科学评分</h3>
                        <p>综合考虑GPA、院校背景、专业相关度等9个维度</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📝</div>
                        <h3>文书生成</h3>
                        <p>为每个目标项目生成个性化申请文书</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">💬</div>
                        <h3>聊天交互</h3>
                        <p>类似ChatGPT的对话式用户体验</p>
                    </div>
                </div>
            </div>
            
            <!-- 启动说明 -->
            <div class="cta-section">
                <h2>🚀 项目已成功启动！</h2>
                <p style="margin: 20px 0;">您可以通过以下方式体验完整功能：</p>
                
                <button class="cta-button" onclick="alert('运行命令: python demo.py')">
                    命令行演示 (已运行)
                </button>
                
                <button class="cta-button" onclick="alert('安装streamlit后运行: streamlit run simple_app.py')">
                    Web界面版本
                </button>
                
                <button class="cta-button" onclick="alert('设置OpenAI API后运行: python run.py')">
                    完整版本
                </button>
                
                <div style="margin-top: 30px; font-size: 0.9rem; opacity: 0.8;">
                    <p>✅ 命令行演示已成功运行，展示了完整的推荐和文书生成功能</p>
                    <p>🔧 Web界面正在安装依赖中，稍后即可使用</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
