"""
简化版启动脚本
"""
import subprocess
import sys
import os
from pathlib import Path

def check_basic_requirements():
    """检查基本依赖"""
    try:
        import streamlit
        import pandas
        import numpy
        print("✅ 基本依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少基本依赖: {e}")
        return False

def install_basic_requirements():
    """安装基本依赖"""
    print("📦 正在安装基本依赖...")
    basic_packages = [
        "streamlit>=1.29.0",
        "pandas>=2.1.4", 
        "numpy>=1.24.3"
    ]
    
    try:
        for package in basic_packages:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print("✅ 基本依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def check_data_files():
    """检查数据文件"""
    required_files = [
        "universities.csv",
        "college.csv", 
        "department.csv",
        "degree_programs.csv",
        "courses.csv",
        "course_selection.csv",
        "graduate_application.csv"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少数据文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 数据文件完整")
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        # 测试数据加载
        sys.path.append(str(Path(__file__).parent / "src"))
        from data_loader import data_loader
        
        universities = data_loader.universities_df
        programs = data_loader.programs_df
        
        print(f"✅ 数据加载成功: {len(universities)}所大学, {len(programs)}个专业")
        
        # 测试推荐引擎
        from simple_recommendation import SimpleRecommendationEngine
        from models import UserProfile, InstitutionType, Country
        
        engine = SimpleRecommendationEngine()
        profile = UserProfile(
            name="测试用户",
            undergraduate_school="北京大学",
            undergraduate_major="计算机科学",
            gpa=3.5,
            institution_type=InstitutionType.C9,
            target_countries=[Country.USA],
            target_majors=["Computer Science"]
        )
        
        recommendations = engine.get_recommendations(profile, top_k=5)
        print(f"✅ 推荐引擎测试成功: 生成{len(recommendations)}个推荐")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎓 留学申请匹配Agent (简化版) 启动检查")
    print("=" * 50)
    
    # 检查基本依赖
    if not check_basic_requirements():
        print("正在安装基本依赖...")
        if not install_basic_requirements():
            print("❌ 启动失败：无法安装依赖")
            return
    
    # 检查数据文件
    if not check_data_files():
        print("❌ 启动失败：数据文件不完整")
        return
    
    # 测试基本功能
    if not test_basic_functionality():
        print("❌ 启动失败：功能测试未通过")
        return
    
    print("\n🚀 启动简化版Streamlit应用...")
    print("=" * 50)
    print("📝 注意：这是简化版本，不需要OpenAI API密钥")
    print("🔧 功能包括：背景分析、智能推荐、文书生成")
    print("🌐 应用将在浏览器中打开: http://localhost:8501")
    print("=" * 50)
    
    # 启动Streamlit应用
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "simple_app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
