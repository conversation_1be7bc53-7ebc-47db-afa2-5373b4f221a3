"""
申请文书生成模块
"""
from typing import Dict, Any, List, Optional
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from langchain.prompts import PromptTemplate

from .models import UserProfile, UniversityProgram, ApplicationDocument


class DocumentGenerator:
    """申请文书生成器"""
    
    def __init__(self, openai_api_key: str):
        """
        初始化文书生成器
        
        Args:
            openai_api_key: OpenAI API密钥
        """
        self.llm = ChatOpenAI(
            model="gpt-4",
            api_key=openai_api_key,
            temperature=0.7
        )
        
        # 文书模板
        self.ps_template = PromptTemplate(
            input_variables=[
                "user_name", "undergraduate_school", "undergraduate_major", "gpa",
                "target_university", "target_program", "target_major",
                "research_experience", "internships", "awards", "resume_highlights",
                "university_ranking", "program_features"
            ],
            template="""
            请为以下学生撰写一份个人陈述(Personal Statement)，要求：

            **学生背景**：
            - 姓名：{user_name}
            - 本科院校：{undergraduate_school}
            - 本科专业：{undergraduate_major}
            - GPA：{gpa}
            - 研究经历：{research_experience}
            - 实习经历：{internships}
            - 获奖情况：{awards}
            - 简历亮点：{resume_highlights}

            **目标申请**：
            - 目标大学：{target_university} (QS排名：{university_ranking})
            - 目标专业：{target_program}
            - 专业领域：{target_major}
            - 项目特色：{program_features}

            **撰写要求**：
            1. 字数控制在800-1000字
            2. 结构清晰：开头引入 → 学术背景 → 研究/实践经历 → 职业目标 → 为什么选择该校该专业 → 结尾
            3. 突出与目标专业的匹配度
            4. 体现对目标学校的了解和向往
            5. 展现个人特色和成长轨迹
            6. 语言地道，逻辑清晰
            7. 避免模板化表达，要有个性化内容

            请撰写一份高质量的个人陈述：
            """
        )
        
        self.sop_template = PromptTemplate(
            input_variables=[
                "user_name", "undergraduate_school", "undergraduate_major", "gpa",
                "target_university", "target_program", "research_interests",
                "career_goals", "why_this_program", "faculty_interests"
            ],
            template="""
            请为以下学生撰写一份目的陈述(Statement of Purpose)，要求：

            **学生信息**：
            - 姓名：{user_name}
            - 本科背景：{undergraduate_school} - {undergraduate_major}
            - GPA：{gpa}

            **申请信息**：
            - 目标大学：{target_university}
            - 目标专业：{target_program}
            - 研究兴趣：{research_interests}
            - 职业目标：{career_goals}
            - 选择该项目的原因：{why_this_program}
            - 感兴趣的教授：{faculty_interests}

            **撰写要求**：
            1. 字数800-1200字
            2. 重点突出学术和研究方面
            3. 明确的研究方向和职业规划
            4. 体现对项目的深入了解
            5. 展现学术潜力和研究能力

            请撰写一份专业的目的陈述：
            """
        )
    
    def generate_personal_statement(
        self, 
        user_profile: UserProfile, 
        target_program: UniversityProgram,
        resume_text: Optional[str] = None
    ) -> ApplicationDocument:
        """
        生成个人陈述
        
        Args:
            user_profile: 用户背景信息
            target_program: 目标项目
            resume_text: 简历文本
            
        Returns:
            申请文书
        """
        # 准备模板变量
        template_vars = {
            "user_name": user_profile.name or "申请者",
            "undergraduate_school": user_profile.undergraduate_school or "本科院校",
            "undergraduate_major": user_profile.undergraduate_major or "本科专业",
            "gpa": str(user_profile.gpa) if user_profile.gpa else "优秀",
            "target_university": target_program.university_name_cn,
            "target_program": target_program.program_name,
            "target_major": ", ".join(user_profile.target_majors) if user_profile.target_majors else "相关专业",
            "research_experience": "有研究经历" if user_profile.research_experience else "暂无研究经历",
            "internships": ", ".join(user_profile.internships) if user_profile.internships else "相关实习经历",
            "awards": ", ".join(user_profile.awards) if user_profile.awards else "学术表现优秀",
            "resume_highlights": resume_text[:200] + "..." if resume_text and len(resume_text) > 200 else resume_text or "丰富的学习和实践经历",
            "university_ranking": str(target_program.qs_ranking),
            "program_features": self._get_program_features(target_program)
        }
        
        # 生成个人陈述
        prompt = self.ps_template.format(**template_vars)
        
        messages = [
            SystemMessage(content="你是一位专业的留学申请文书顾问，擅长撰写高质量的个人陈述。"),
            HumanMessage(content=prompt)
        ]
        
        response = self.llm.invoke(messages)
        
        return ApplicationDocument(
            document_type="Personal Statement",
            content=response.content,
            target_program=target_program
        )
    
    def generate_statement_of_purpose(
        self,
        user_profile: UserProfile,
        target_program: UniversityProgram,
        research_interests: str = "",
        career_goals: str = ""
    ) -> ApplicationDocument:
        """
        生成目的陈述
        
        Args:
            user_profile: 用户背景信息
            target_program: 目标项目
            research_interests: 研究兴趣
            career_goals: 职业目标
            
        Returns:
            申请文书
        """
        template_vars = {
            "user_name": user_profile.name or "申请者",
            "undergraduate_school": user_profile.undergraduate_school or "本科院校",
            "undergraduate_major": user_profile.undergraduate_major or "本科专业",
            "gpa": str(user_profile.gpa) if user_profile.gpa else "优秀",
            "target_university": target_program.university_name_cn,
            "target_program": target_program.program_name,
            "research_interests": research_interests or self._infer_research_interests(user_profile, target_program),
            "career_goals": career_goals or self._infer_career_goals(user_profile, target_program),
            "why_this_program": self._generate_why_this_program(user_profile, target_program),
            "faculty_interests": self._get_faculty_info(target_program)
        }
        
        prompt = self.sop_template.format(**template_vars)
        
        messages = [
            SystemMessage(content="你是一位专业的留学申请文书顾问，擅长撰写学术性强的目的陈述。"),
            HumanMessage(content=prompt)
        ]
        
        response = self.llm.invoke(messages)
        
        return ApplicationDocument(
            document_type="Statement of Purpose",
            content=response.content,
            target_program=target_program
        )
    
    def generate_diversity_essay(
        self,
        user_profile: UserProfile,
        target_program: UniversityProgram
    ) -> ApplicationDocument:
        """
        生成多样性文书
        
        Args:
            user_profile: 用户背景信息
            target_program: 目标项目
            
        Returns:
            申请文书
        """
        prompt = f"""
        请为以下学生撰写一份多样性文书(Diversity Essay)：

        **学生背景**：
        - 本科院校：{user_profile.undergraduate_school}
        - 专业：{user_profile.undergraduate_major}
        - 目标申请：{target_program.university_name_cn} - {target_program.program_name}

        **撰写要求**：
        1. 字数500-700字
        2. 突出个人独特的背景、经历或观点
        3. 展现如何为校园多样性做出贡献
        4. 体现跨文化理解和包容性
        5. 避免刻板印象，展现真实的个人特色

        请撰写一份有感染力的多样性文书：
        """
        
        messages = [
            SystemMessage(content="你是一位专业的留学申请文书顾问，擅长撰写展现个人特色的多样性文书。"),
            HumanMessage(content=prompt)
        ]
        
        response = self.llm.invoke(messages)
        
        return ApplicationDocument(
            document_type="Diversity Essay",
            content=response.content,
            target_program=target_program
        )
    
    def generate_why_school_essay(
        self,
        user_profile: UserProfile,
        target_program: UniversityProgram
    ) -> ApplicationDocument:
        """
        生成Why School文书
        
        Args:
            user_profile: 用户背景信息
            target_program: 目标项目
            
        Returns:
            申请文书
        """
        prompt = f"""
        请为以下学生撰写一份"Why {target_program.university_name}"文书：

        **学生信息**：
        - 背景：{user_profile.undergraduate_school} - {user_profile.undergraduate_major}
        - 目标：{target_program.university_name_cn} - {target_program.program_name}
        - QS排名：{target_program.qs_ranking}

        **撰写要求**：
        1. 字数400-600字
        2. 具体说明选择该校的原因
        3. 展现对学校和项目的深入了解
        4. 体现个人目标与学校资源的匹配
        5. 避免泛泛而谈，要有具体的细节

        请撰写一份有说服力的Why School文书：
        """
        
        messages = [
            SystemMessage(content="你是一位专业的留学申请文书顾问，擅长撰写针对性强的Why School文书。"),
            HumanMessage(content=prompt)
        ]
        
        response = self.llm.invoke(messages)
        
        return ApplicationDocument(
            document_type="Why School Essay",
            content=response.content,
            target_program=target_program
        )
    
    def _get_program_features(self, program: UniversityProgram) -> str:
        """获取项目特色"""
        features = []
        
        if program.qs_ranking <= 10:
            features.append("世界顶尖大学")
        elif program.qs_ranking <= 50:
            features.append("世界一流大学")
        
        if "Computer" in program.program_name or "AI" in program.program_name:
            features.append("前沿的计算机科学教育")
        elif "Engineering" in program.program_name:
            features.append("卓越的工程教育")
        elif "Business" in program.program_name:
            features.append("顶尖的商学教育")
        
        if program.core_courses:
            features.append(f"核心课程包括{', '.join(program.core_courses[:3])}")
        
        return "；".join(features) if features else "优质的教育资源和学术环境"
    
    def _infer_research_interests(self, user_profile: UserProfile, program: UniversityProgram) -> str:
        """推断研究兴趣"""
        interests = []
        
        if user_profile.undergraduate_major:
            if "计算机" in user_profile.undergraduate_major:
                interests.extend(["机器学习", "人工智能", "数据科学"])
            elif "电子" in user_profile.undergraduate_major:
                interests.extend(["信号处理", "通信系统", "嵌入式系统"])
            elif "机械" in user_profile.undergraduate_major:
                interests.extend(["智能制造", "机器人技术", "自动化"])
        
        if program.program_name:
            if "AI" in program.program_name or "Artificial Intelligence" in program.program_name:
                interests.extend(["深度学习", "自然语言处理", "计算机视觉"])
            elif "Data Science" in program.program_name:
                interests.extend(["大数据分析", "统计建模", "预测分析"])
        
        return "、".join(interests[:3]) if interests else "相关领域的前沿研究"
    
    def _infer_career_goals(self, user_profile: UserProfile, program: UniversityProgram) -> str:
        """推断职业目标"""
        if "Computer" in program.program_name or "AI" in program.program_name:
            return "成为人工智能领域的专家，在科技公司或研究机构从事前沿技术研发"
        elif "Business" in program.program_name:
            return "在跨国企业担任管理职位，推动商业创新和全球化发展"
        elif "Engineering" in program.program_name:
            return "在工程技术领域做出贡献，解决实际问题并推动技术进步"
        else:
            return "在相关专业领域深入发展，为社会进步贡献专业知识和技能"
    
    def _generate_why_this_program(self, user_profile: UserProfile, program: UniversityProgram) -> str:
        """生成选择该项目的原因"""
        reasons = []
        
        if program.qs_ranking <= 20:
            reasons.append(f"作为QS世界排名第{program.qs_ranking}位的顶尖大学")
        
        if program.core_courses:
            reasons.append(f"该项目的核心课程如{', '.join(program.core_courses[:2])}与我的学术兴趣高度匹配")
        
        reasons.append("该项目的国际化教育环境和优质师资")
        
        return "；".join(reasons)
    
    def _get_faculty_info(self, program: UniversityProgram) -> str:
        """获取教授信息（简化版）"""
        # 这里简化处理，实际应该从数据库获取真实的教授信息
        return f"对{program.university_name_cn}在{program.program_name}领域的知名教授的研究工作很感兴趣"
