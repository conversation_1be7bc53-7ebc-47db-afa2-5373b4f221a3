"""
LangGraph Agent工作流定义
"""
from typing import Dict, Any, List, Optional
from langgraph.graph import StateGraph, END
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
import json

from .models import AgentState, UserProfile, TranscriptData, UniversityProgram
from .simple_recommendation import SimpleRecommendationEngine
from .simple_document_generator import SimpleDocumentGenerator


class StudyAbroadAgent:
    """留学申请Agent"""
    
    def __init__(self, openai_api_key: str):
        """
        初始化Agent
        
        Args:
            openai_api_key: OpenAI API密钥
        """
        self.llm = ChatOpenAI(
            model="gpt-4",
            api_key=openai_api_key,
            temperature=0.3
        )
        
        # 初始化各个模块
        self.recommendation_engine = SimpleRecommendationEngine()
        self.document_generator = SimpleDocumentGenerator()
        
        # 构建工作流图
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建LangGraph工作流"""
        workflow = StateGraph(AgentState)
        
        # 添加节点
        workflow.add_node("start", self.start_conversation)
        workflow.add_node("collect_basic_info", self.collect_basic_info)
        workflow.add_node("process_transcript", self.process_transcript)
        workflow.add_node("collect_preferences", self.collect_preferences)
        workflow.add_node("generate_recommendations", self.generate_recommendations)
        workflow.add_node("select_programs", self.select_programs)
        workflow.add_node("process_resume", self.process_resume)
        workflow.add_node("generate_documents", self.generate_documents)
        workflow.add_node("finalize", self.finalize_results)
        
        # 设置入口点
        workflow.set_entry_point("start")
        
        # 添加边
        workflow.add_edge("start", "collect_basic_info")
        workflow.add_edge("collect_basic_info", "process_transcript")
        workflow.add_edge("process_transcript", "collect_preferences")
        workflow.add_edge("collect_preferences", "generate_recommendations")
        workflow.add_edge("generate_recommendations", "select_programs")
        workflow.add_edge("select_programs", "process_resume")
        workflow.add_edge("process_resume", "generate_documents")
        workflow.add_edge("generate_documents", "finalize")
        workflow.add_edge("finalize", END)
        
        return workflow.compile()
    
    def start_conversation(self, state: AgentState) -> AgentState:
        """开始对话"""
        welcome_message = """
        🎓 欢迎使用留学申请匹配Agent！
        
        我将帮助您：
        1. 📊 分析您的学术背景和成绩
        2. 🎯 了解您的留学偏好
        3. 🏫 推荐最适合的大学和专业
        4. 📝 生成个性化申请文书
        
        让我们开始吧！首先，请告诉我您的基本信息。
        """
        
        state.conversation_history.append({
            "role": "assistant",
            "content": welcome_message
        })
        state.current_step = "collect_basic_info"
        
        return state
    
    def collect_basic_info(self, state: AgentState) -> AgentState:
        """收集基本信息"""
        if not state.user_profile.name:
            prompt = """
            请提供以下基本信息：
            
            1. 姓名
            2. 年龄
            3. 本科院校名称
            4. 本科专业
            5. 院校类型（C9/985/211/双一流/普通本科/普通专科）
            6. 毕业年份
            7. 当前GPA（如果知道的话）
            
            您可以逐一回答，或者一次性提供所有信息。
            """
            
            state.conversation_history.append({
                "role": "assistant", 
                "content": prompt
            })
        
        return state
    
    def process_transcript(self, state: AgentState) -> AgentState:
        """处理成绩单"""
        if not state.transcript_data:
            prompt = """
            现在请上传您的成绩单图片，我将帮您：
            
            1. 📷 识别成绩单中的课程和分数
            2. 🧮 计算准确的GPA
            3. ✅ 让您确认和修正识别结果
            
            请上传成绩单图片，或者如果您已经知道准确的GPA，也可以直接告诉我。
            """
            
            state.conversation_history.append({
                "role": "assistant",
                "content": prompt
            })
        
        return state
    
    def collect_preferences(self, state: AgentState) -> AgentState:
        """收集留学偏好"""
        if not state.user_profile.target_countries:
            prompt = """
            请告诉我您的留学偏好：
            
            🌍 **目标国家/地区**（可多选）：
            - 美国
            - 英国  
            - 加拿大
            - 澳大利亚
            - 新加坡
            - 德国
            - 法国
            - 日本
            - 韩国
            - 其他
            
            🎓 **目标专业领域**：
            - 计算机科学/AI
            - 工程类
            - 商科/管理
            - 理科
            - 医学
            - 人文社科
            - 艺术设计
            - 其他
            
            📅 **希望入学时间**：
            - 2025年春季
            - 2025年秋季
            - 2026年春季
            - 2026年秋季
            """
            
            state.conversation_history.append({
                "role": "assistant",
                "content": prompt
            })
        
        return state
    
    def generate_recommendations(self, state: AgentState) -> AgentState:
        """生成推荐"""
        if not state.recommendations:
            # 使用推荐引擎生成推荐
            recommendations = self.recommendation_engine.get_recommendations(
                state.user_profile, top_k=20
            )
            state.recommendations = recommendations
            
            # 格式化推荐结果
            result_message = self._format_recommendations(recommendations)
            
            state.conversation_history.append({
                "role": "assistant",
                "content": result_message
            })
        
        return state
    
    def select_programs(self, state: AgentState) -> AgentState:
        """选择项目"""
        if not state.selected_programs:
            prompt = """
            请从上述20个推荐中选择您感兴趣的学校（最多10个）。
            
            您可以通过以下方式选择：
            1. 输入学校编号，如：1,3,5,7,9
            2. 输入学校名称
            3. 告诉我您的偏好，我来帮您筛选
            
            选择完成后，我将为您生成个性化的申请文书。
            """
            
            state.conversation_history.append({
                "role": "assistant",
                "content": prompt
            })
        
        return state
    
    def process_resume(self, state: AgentState) -> AgentState:
        """处理简历"""
        if not state.resume_text:
            prompt = """
            请上传您的简历，我将分析您的：
            
            📚 **学术经历**
            🔬 **研究经验**  
            💼 **实习工作经历**
            🏆 **获奖情况**
            📄 **发表论文**
            
            这些信息将帮助我为每个目标学校生成个性化的申请文书。
            
            您可以上传简历文件，或者直接在对话中描述您的经历。
            """
            
            state.conversation_history.append({
                "role": "assistant",
                "content": prompt
            })
        
        return state
    
    def generate_documents(self, state: AgentState) -> AgentState:
        """生成申请文书"""
        if not state.documents and state.selected_programs:
            # 为每个选中的项目生成文书
            documents = []
            for program in state.selected_programs:
                doc = self.document_generator.generate_personal_statement(
                    state.user_profile, program, state.resume_text
                )
                documents.append(doc)
            
            state.documents = documents
            
            prompt = f"""
            ✅ 已为您的{len(state.selected_programs)}个目标项目生成个性化申请文书！
            
            每份文书都根据：
            - 您的学术背景和经历
            - 目标学校的特色和要求  
            - 专业的匹配度
            
            进行了定制化撰写。您可以查看、修改和完善这些文书。
            
            是否需要我展示某个学校的文书内容？
            """
            
            state.conversation_history.append({
                "role": "assistant",
                "content": prompt
            })
        
        return state
    
    def finalize_results(self, state: AgentState) -> AgentState:
        """完成流程"""
        summary = f"""
        🎉 **留学申请规划完成！**
        
        📊 **您的背景分析**：
        - 本科院校：{state.user_profile.undergraduate_school}
        - 专业：{state.user_profile.undergraduate_major}
        - GPA：{state.user_profile.gpa}
        
        🎯 **推荐结果**：
        - 总推荐学校：{len(state.recommendations)}所
        - 您选择的目标：{len(state.selected_programs)}所
        - 生成文书：{len(state.documents)}份
        
        📝 **下一步建议**：
        1. 仔细审阅申请文书
        2. 准备其他申请材料
        3. 关注申请截止日期
        4. 准备语言考试（如需要）
        
        祝您申请顺利！🍀
        """
        
        state.conversation_history.append({
            "role": "assistant",
            "content": summary
        })
        state.current_step = "completed"
        
        return state
    
    def _format_recommendations(self, recommendations: List) -> str:
        """格式化推荐结果"""
        if not recommendations:
            return "抱歉，根据您的条件暂时没有找到合适的推荐。"
        
        result = "🎓 **为您推荐以下20所大学和专业**：\n\n"
        
        # 按匹配类型分组
        reach_schools = [r for r in recommendations if r.match_type == "冲刺"]
        match_schools = [r for r in recommendations if r.match_type == "匹配"] 
        safety_schools = [r for r in recommendations if r.match_type == "保底"]
        
        if reach_schools:
            result += "🚀 **冲刺院校**（4所）：\n"
            for i, rec in enumerate(reach_schools, 1):
                result += self._format_single_recommendation(i, rec)
            result += "\n"
        
        if match_schools:
            result += "🎯 **匹配院校**（10所）：\n"
            for i, rec in enumerate(match_schools, len(reach_schools) + 1):
                result += self._format_single_recommendation(i, rec)
            result += "\n"
        
        if safety_schools:
            result += "🛡️ **保底院校**（6所）：\n"
            for i, rec in enumerate(safety_schools, len(reach_schools) + len(match_schools) + 1):
                result += self._format_single_recommendation(i, rec)
        
        return result
    
    def _format_single_recommendation(self, index: int, rec) -> str:
        """格式化单个推荐"""
        return f"""
        **{index}. {rec.program.university_name_cn}** (QS#{rec.program.qs_ranking})
        - 专业：{rec.program.program_name}
        - 学制：{rec.program.duration}
        - 学费：{rec.program.tuition_fee}
        - GPA要求：{rec.program.gpa_requirement}
        - 语言要求：{rec.program.language_requirement}
        - 申请截止：{rec.program.application_deadline}
        - 匹配度：{rec.match_score:.1f}%
        - 推荐理由：{rec.recommendation_reason}
        
        """
    
    def process_user_input(self, state: AgentState, user_input: str) -> AgentState:
        """处理用户输入"""
        # 添加用户消息到历史
        state.conversation_history.append({
            "role": "user",
            "content": user_input
        })
        
        # 根据当前步骤处理输入
        if state.current_step == "collect_basic_info":
            state = self._parse_basic_info(state, user_input)
        elif state.current_step == "process_transcript":
            state = self._parse_transcript_info(state, user_input)
        elif state.current_step == "collect_preferences":
            state = self._parse_preferences(state, user_input)
        elif state.current_step == "select_programs":
            state = self._parse_program_selection(state, user_input)
        elif state.current_step == "process_resume":
            state = self._parse_resume_info(state, user_input)
        
        return state
    
    def _parse_basic_info(self, state: AgentState, user_input: str) -> AgentState:
        """解析基本信息"""
        # 使用LLM解析用户输入
        system_prompt = """
        请从用户输入中提取以下信息，以JSON格式返回：
        {
            "name": "姓名",
            "age": 年龄(数字),
            "undergraduate_school": "本科院校",
            "undergraduate_major": "本科专业", 
            "institution_type": "院校类型",
            "degree_year": 毕业年份(数字),
            "gpa": GPA(数字)
        }
        
        如果某些信息没有提供，请设为null。
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_input)
        ]
        
        try:
            response = self.llm.invoke(messages)
            info = json.loads(response.content)
            
            # 更新用户档案
            for key, value in info.items():
                if value is not None:
                    setattr(state.user_profile, key, value)
            
            # 检查是否收集完整
            if all([state.user_profile.name, state.user_profile.undergraduate_school, 
                   state.user_profile.undergraduate_major]):
                state.current_step = "process_transcript"
                
        except Exception as e:
            print(f"解析基本信息错误: {e}")
        
        return state
    
    def _parse_transcript_info(self, state: AgentState, user_input: str) -> AgentState:
        """解析成绩单信息"""
        # 这里简化处理，实际需要处理图片上传
        if "gpa" in user_input.lower() or any(char.isdigit() for char in user_input):
            try:
                # 提取GPA数值
                import re
                gpa_match = re.search(r'(\d+\.?\d*)', user_input)
                if gpa_match:
                    gpa = float(gpa_match.group(1))
                    if gpa <= 4.0:
                        state.user_profile.gpa = gpa
                    elif gpa <= 100:  # 百分制转换
                        state.user_profile.gpa = gpa / 25  # 简化转换
                    
                    state.current_step = "collect_preferences"
            except:
                pass
        
        return state
    
    def _parse_preferences(self, state: AgentState, user_input: str) -> AgentState:
        """解析偏好信息"""
        # 使用LLM解析偏好
        system_prompt = """
        请从用户输入中提取留学偏好信息，以JSON格式返回：
        {
            "target_countries": ["国家1", "国家2"],
            "target_majors": ["专业1", "专业2"],
            "preferred_term": "入学时间"
        }
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_input)
        ]
        
        try:
            response = self.llm.invoke(messages)
            prefs = json.loads(response.content)
            
            if prefs.get('target_countries'):
                state.user_profile.target_countries = prefs['target_countries']
            if prefs.get('target_majors'):
                state.user_profile.target_majors = prefs['target_majors']
            if prefs.get('preferred_term'):
                state.user_profile.preferred_term = prefs['preferred_term']
            
            if state.user_profile.target_countries and state.user_profile.target_majors:
                state.current_step = "generate_recommendations"
                
        except Exception as e:
            print(f"解析偏好信息错误: {e}")
        
        return state
    
    def _parse_program_selection(self, state: AgentState, user_input: str) -> AgentState:
        """解析项目选择"""
        # 解析用户选择的学校编号或名称
        import re
        
        # 提取数字编号
        numbers = re.findall(r'\d+', user_input)
        if numbers:
            selected_indices = [int(n) - 1 for n in numbers if 0 <= int(n) - 1 < len(state.recommendations)]
            state.selected_programs = [state.recommendations[i].program for i in selected_indices[:10]]
            state.current_step = "process_resume"
        
        return state
    
    def _parse_resume_info(self, state: AgentState, user_input: str) -> AgentState:
        """解析简历信息"""
        # 简化处理，直接保存用户输入作为简历文本
        state.resume_text = user_input
        state.current_step = "generate_documents"
        return state
