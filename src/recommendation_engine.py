"""
推荐引擎模块
"""
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import faiss
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain.schema import HumanMessage, SystemMessage

from .models import UserProfile, UniversityProgram, RecommendationResult, InstitutionType, Country
from .data_loader import data_loader


class RecommendationEngine:
    """推荐引擎"""
    
    def __init__(self, openai_api_key: str):
        """
        初始化推荐引擎
        
        Args:
            openai_api_key: OpenAI API密钥
        """
        self.llm = ChatOpenAI(
            model="gpt-4",
            api_key=openai_api_key,
            temperature=0.3
        )
        self.embeddings = OpenAIEmbeddings(api_key=openai_api_key)
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        
        # 院校权重映射
        self.institution_weights = {
            InstitutionType.C9: 1.0,
            InstitutionType.UNIVERSITY_985: 0.95,
            InstitutionType.UNIVERSITY_211: 0.85,
            InstitutionType.DOUBLE_FIRST_CLASS: 0.80,
            InstitutionType.REGULAR_UNDERGRADUATE: 0.70,
            InstitutionType.REGULAR_COLLEGE: 0.60
        }
    
    def calculate_match_score(self, user_profile: UserProfile, program: Dict[str, Any]) -> float:
        """
        计算匹配分数
        
        Args:
            user_profile: 用户背景信息
            program: 大学专业信息
            
        Returns:
            匹配分数 (0-100)
        """
        score = 0.0
        
        # 1. GPA分数 (30%)
        if user_profile.gpa and program.get('gpa_requirement'):
            gpa_score = self._calculate_gpa_score(user_profile.gpa, program['gpa_requirement'])
            institution_weight = self.institution_weights.get(user_profile.institution_type, 0.7)
            score += 30 * gpa_score * institution_weight
        
        # 2. 院校声誉分数 (10%)
        if user_profile.undergraduate_school:
            reputation_score = self._calculate_reputation_score(user_profile.undergraduate_school)
            score += 10 * reputation_score
        
        # 3. 专业相关度 (15%)
        if user_profile.undergraduate_major and user_profile.target_majors:
            major_score = self._calculate_major_similarity(
                user_profile.undergraduate_major, 
                program.get('program_name', ''),
                user_profile.target_majors
            )
            score += 15 * major_score
        
        # 4. 语言成绩 (10%)
        language_score = self._calculate_language_score(user_profile, program.get('language_requirement', ''))
        score += 10 * language_score
        
        # 5. 标准化考试 (10%)
        test_score = self._calculate_test_score(user_profile)
        score += 10 * test_score
        
        # 6. 研究经历 (10%)
        research_score = self._calculate_research_score(user_profile)
        score += 10 * research_score
        
        # 7. 课外活动/奖项 (5%)
        activity_score = self._calculate_activity_score(user_profile)
        score += 5 * activity_score
        
        # 8. 实习/工作经历 (5%)
        work_score = self._calculate_work_score(user_profile)
        score += 5 * work_score
        
        # 9. 推荐信 (5%)
        # 这里简化处理，实际需要更复杂的逻辑
        recommendation_score = 0.7  # 默认分数
        score += 5 * recommendation_score
        
        return min(score, 100.0)
    
    def _calculate_gpa_score(self, user_gpa: float, required_gpa: float) -> float:
        """计算GPA分数"""
        if user_gpa >= required_gpa + 0.3:
            return 1.0
        elif user_gpa >= required_gpa:
            return 0.8
        elif user_gpa >= required_gpa - 0.2:
            return 0.6
        elif user_gpa >= required_gpa - 0.4:
            return 0.4
        else:
            return 0.2
    
    def _calculate_reputation_score(self, school_name: str) -> float:
        """计算院校声誉分数"""
        # 简化处理，实际需要更复杂的院校排名数据
        prestigious_keywords = ['清华', '北大', '复旦', '上交', '浙大', '中科大', '南大', '哈工大']
        if any(keyword in school_name for keyword in prestigious_keywords):
            return 1.0
        return 0.7
    
    def _calculate_major_similarity(self, user_major: str, program_name: str, target_majors: List[str]) -> float:
        """计算专业相关度"""
        # 使用TF-IDF计算相似度
        texts = [user_major, program_name] + target_majors
        try:
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(texts)
            similarity_matrix = cosine_similarity(tfidf_matrix)
            
            # 计算用户专业与目标专业的相似度
            user_program_sim = similarity_matrix[0, 1]
            user_target_sim = np.mean([similarity_matrix[0, i+2] for i in range(len(target_majors))])
            program_target_sim = np.mean([similarity_matrix[1, i+2] for i in range(len(target_majors))])
            
            return (user_program_sim * 0.3 + user_target_sim * 0.3 + program_target_sim * 0.4)
        except:
            return 0.5
    
    def _calculate_language_score(self, user_profile: UserProfile, requirement: str) -> float:
        """计算语言成绩分数"""
        if not requirement:
            return 0.7
        
        # 解析语言要求
        if 'IELTS' in requirement.upper():
            required_score = self._extract_ielts_score(requirement)
            if user_profile.ielts_score:
                if user_profile.ielts_score >= required_score + 0.5:
                    return 1.0
                elif user_profile.ielts_score >= required_score:
                    return 0.8
                else:
                    return 0.4
        elif 'TOEFL' in requirement.upper():
            required_score = self._extract_toefl_score(requirement)
            if user_profile.toefl_score:
                if user_profile.toefl_score >= required_score + 10:
                    return 1.0
                elif user_profile.toefl_score >= required_score:
                    return 0.8
                else:
                    return 0.4
        
        return 0.5
    
    def _extract_ielts_score(self, requirement: str) -> float:
        """从要求中提取雅思分数"""
        import re
        match = re.search(r'IELTS\s*(\d+\.?\d*)', requirement.upper())
        return float(match.group(1)) if match else 6.5
    
    def _extract_toefl_score(self, requirement: str) -> int:
        """从要求中提取托福分数"""
        import re
        match = re.search(r'TOEFL\s*(\d+)', requirement.upper())
        return int(match.group(1)) if match else 90
    
    def _calculate_test_score(self, user_profile: UserProfile) -> float:
        """计算标准化考试分数"""
        score = 0.0
        
        if user_profile.gre_verbal and user_profile.gre_quantitative:
            # GRE分数评估
            verbal_score = min(user_profile.gre_verbal / 170, 1.0)
            quant_score = min(user_profile.gre_quantitative / 170, 1.0)
            score = (verbal_score + quant_score) / 2
        elif user_profile.gmat_score:
            # GMAT分数评估
            score = min(user_profile.gmat_score / 800, 1.0)
        else:
            score = 0.5  # 默认分数
        
        return score
    
    def _calculate_research_score(self, user_profile: UserProfile) -> float:
        """计算研究经历分数"""
        score = 0.0
        
        if user_profile.research_experience:
            score += 0.5
        
        if user_profile.publications:
            # 根据发表论文数量和质量评分
            score += min(len(user_profile.publications) * 0.2, 0.5)
        
        return min(score, 1.0)
    
    def _calculate_activity_score(self, user_profile: UserProfile) -> float:
        """计算课外活动分数"""
        score = 0.0
        
        if user_profile.awards:
            score += min(len(user_profile.awards) * 0.2, 0.6)
        
        if user_profile.patents:
            score += min(len(user_profile.patents) * 0.3, 0.4)
        
        return min(score, 1.0)
    
    def _calculate_work_score(self, user_profile: UserProfile) -> float:
        """计算工作经历分数"""
        score = 0.0
        
        if user_profile.internships:
            # 根据实习经历评分
            prestigious_companies = ['Google', 'Microsoft', '腾讯', '阿里巴巴', '字节跳动', '华为']
            for internship in user_profile.internships:
                if any(company in internship for company in prestigious_companies):
                    score += 0.4
                else:
                    score += 0.2
        
        if user_profile.work_experience:
            score += min(len(user_profile.work_experience) * 0.3, 0.4)
        
        return min(score, 1.0)
    
    def get_recommendations(self, user_profile: UserProfile, top_k: int = 20) -> List[RecommendationResult]:
        """
        获取推荐结果
        
        Args:
            user_profile: 用户背景信息
            top_k: 返回推荐数量
            
        Returns:
            推荐结果列表
        """
        # 1. 根据目标国家筛选大学
        target_countries = [country.value for country in user_profile.target_countries] if user_profile.target_countries else []
        if target_countries:
            universities = data_loader.get_universities_by_country(target_countries)
        else:
            universities = data_loader.universities_df
        
        # 2. 根据目标专业筛选专业
        if user_profile.target_majors:
            programs = data_loader.get_programs_by_major_keywords(user_profile.target_majors)
        else:
            programs = data_loader.programs_df
        
        # 3. 合并大学和专业信息
        merged_data = programs.merge(universities, on='university_id', how='inner')
        
        # 4. 计算匹配分数
        recommendations = []
        for _, row in merged_data.iterrows():
            match_score = self.calculate_match_score(user_profile, row.to_dict())
            
            # 确定匹配类型
            if match_score >= 85:
                match_type = "冲刺"
            elif match_score >= 70:
                match_type = "匹配"
            else:
                match_type = "保底"
            
            # 获取核心课程
            core_courses = self._get_core_courses(row['program_id'])
            
            program = UniversityProgram(
                university_id=str(row['university_id']),
                university_name=row['university_name'],
                university_name_cn=row['university_name_cn'],
                program_name=row['program_name'],
                degree=row['degree'],
                duration=row['duration'],
                tuition_fee=row['intl_student_fee'],
                gpa_requirement=row['gpa_requirement'],
                language_requirement=row['language_req'],
                application_deadline=row['application_deadline'],
                qs_ranking=row['qs_ranking'],
                official_website=row['official_website'],
                core_courses=core_courses
            )
            
            recommendation = RecommendationResult(
                program=program,
                match_score=match_score,
                match_type=match_type,
                recommendation_reason=self._generate_recommendation_reason(user_profile, program, match_score)
            )
            
            recommendations.append(recommendation)
        
        # 5. 排序并返回前top_k个
        recommendations.sort(key=lambda x: x.match_score, reverse=True)
        
        # 6. 确保推荐分布合理
        return self._balance_recommendations(recommendations[:top_k])
    
    def _get_core_courses(self, program_id: str) -> List[str]:
        """获取专业核心课程"""
        courses = data_loader.get_program_courses(program_id)
        # 返回前5门必修课程
        core_courses = [course['course_name'] for course in courses 
                       if course['course_type'] == '必修课'][:5]
        return core_courses
    
    def _generate_recommendation_reason(self, user_profile: UserProfile, program: UniversityProgram, score: float) -> str:
        """生成推荐理由"""
        reasons = []
        
        if score >= 85:
            reasons.append(f"您的背景与{program.university_name_cn}的{program.program_name}专业高度匹配")
        elif score >= 70:
            reasons.append(f"您的条件符合{program.university_name_cn}的录取要求")
        else:
            reasons.append(f"{program.university_name_cn}可作为保底选择")
        
        if user_profile.gpa and user_profile.gpa >= program.gpa_requirement:
            reasons.append(f"您的GPA {user_profile.gpa} 满足要求")
        
        if program.qs_ranking <= 50:
            reasons.append(f"QS世界排名第{program.qs_ranking}位，声誉卓著")
        
        return "；".join(reasons)
    
    def _balance_recommendations(self, recommendations: List[RecommendationResult]) -> List[RecommendationResult]:
        """平衡推荐结果分布"""
        # 确保有合理的冲刺、匹配、保底分布
        reach_schools = [r for r in recommendations if r.match_type == "冲刺"][:4]
        match_schools = [r for r in recommendations if r.match_type == "匹配"][:10]
        safety_schools = [r for r in recommendations if r.match_type == "保底"][:6]

        balanced = reach_schools + match_schools + safety_schools
        return balanced[:20]
