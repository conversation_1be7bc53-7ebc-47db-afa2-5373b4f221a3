"""
OCR成绩单识别和GPA计算模块
"""
import cv2
import numpy as np
import pytesseract
from PIL import Image
import re
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage

from .models import CourseGrade, TranscriptData


class OCRProcessor:
    """OCR处理器，用于识别成绩单"""
    
    def __init__(self, openai_api_key: str):
        """
        初始化OCR处理器
        
        Args:
            openai_api_key: OpenAI API密钥
        """
        self.llm = ChatOpenAI(
            model="gpt-4-vision-preview",
            api_key=openai_api_key,
            temperature=0
        )
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            
        Returns:
            预处理后的图像
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 去噪
        denoised = cv2.medianBlur(gray, 3)
        
        # 二值化
        _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 形态学操作
        kernel = np.ones((1, 1), np.uint8)
        processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return processed
    
    def extract_text_with_tesseract(self, image: np.ndarray) -> str:
        """
        使用Tesseract提取文本
        
        Args:
            image: 输入图像
            
        Returns:
            提取的文本
        """
        # 预处理图像
        processed_image = self.preprocess_image(image)
        
        # 配置Tesseract
        config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz中文.，：；（）【】'
        
        # 提取文本
        text = pytesseract.image_to_string(processed_image, lang='chi_sim+eng', config=config)
        
        return text
    
    def extract_transcript_with_llm(self, image_path: str) -> TranscriptData:
        """
        使用LLM提取成绩单信息
        
        Args:
            image_path: 图像路径
            
        Returns:
            成绩单数据
        """
        system_prompt = """
        你是一个专业的成绩单识别专家。请分析上传的成绩单图片，提取以下信息：

        1. 课程名称
        2. 成绩/分数
        3. 学分
        4. 学期（如果有）

        请以JSON格式返回结果，格式如下：
        {
            "courses": [
                {
                    "course_name": "课程名称",
                    "grade": 分数(数字),
                    "credit": 学分(数字),
                    "semester": "学期"
                }
            ],
            "overall_gpa": GPA总分(如果能计算出来)
        }

        注意：
        - 成绩可能是百分制、五分制或四分制，请统一转换为四分制GPA
        - 如果无法识别某些信息，请标记为null
        - 确保数字格式正确
        """
        
        # 这里简化处理，实际应该使用GPT-4V
        # 由于当前环境限制，我们使用文本处理方式
        try:
            # 读取图像并转换为文本
            image = cv2.imread(image_path)
            text = self.extract_text_with_tesseract(image)
            
            # 使用LLM解析文本
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"请解析以下成绩单文本：\n{text}")
            ]
            
            response = self.llm.invoke(messages)
            
            # 解析响应（这里需要更robust的JSON解析）
            import json
            try:
                data = json.loads(response.content)
                courses = [CourseGrade(**course) for course in data.get('courses', [])]
                return TranscriptData(
                    courses=courses,
                    overall_gpa=data.get('overall_gpa')
                )
            except json.JSONDecodeError:
                # 如果JSON解析失败，返回空数据
                return TranscriptData(courses=[])
                
        except Exception as e:
            print(f"OCR处理错误: {e}")
            return TranscriptData(courses=[])
    
    def parse_transcript_text(self, text: str) -> TranscriptData:
        """
        解析成绩单文本
        
        Args:
            text: 成绩单文本
            
        Returns:
            成绩单数据
        """
        courses = []
        lines = text.split('\n')
        
        # 简单的正则表达式匹配
        # 这里需要根据实际成绩单格式调整
        course_pattern = r'([^0-9]+)\s+([0-9.]+)\s+([0-9.]+)'
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            match = re.search(course_pattern, line)
            if match:
                course_name = match.group(1).strip()
                grade = float(match.group(2))
                credit = float(match.group(3))
                
                # 转换成绩为4分制GPA
                if grade > 10:  # 百分制
                    gpa_grade = self.convert_to_gpa(grade, 'percentage')
                elif grade > 4:  # 五分制
                    gpa_grade = self.convert_to_gpa(grade, 'five_point')
                else:  # 四分制
                    gpa_grade = grade
                
                courses.append(CourseGrade(
                    course_name=course_name,
                    grade=gpa_grade,
                    credit=credit
                ))
        
        # 计算总GPA
        if courses:
            total_credits = sum(course.credit for course in courses)
            weighted_sum = sum(course.grade * course.credit for course in courses)
            overall_gpa = weighted_sum / total_credits if total_credits > 0 else 0
        else:
            overall_gpa = None
        
        return TranscriptData(courses=courses, overall_gpa=overall_gpa)
    
    def convert_to_gpa(self, score: float, scale: str) -> float:
        """
        转换成绩为4分制GPA
        
        Args:
            score: 原始成绩
            scale: 成绩制度 ('percentage', 'five_point', 'four_point')
            
        Returns:
            4分制GPA
        """
        if scale == 'percentage':
            # 百分制转4分制
            if score >= 90:
                return 4.0
            elif score >= 85:
                return 3.7
            elif score >= 82:
                return 3.3
            elif score >= 78:
                return 3.0
            elif score >= 75:
                return 2.7
            elif score >= 72:
                return 2.3
            elif score >= 68:
                return 2.0
            elif score >= 64:
                return 1.7
            elif score >= 60:
                return 1.0
            else:
                return 0.0
        elif scale == 'five_point':
            # 五分制转4分制
            return min(score * 0.8, 4.0)
        else:
            # 四分制
            return min(score, 4.0)
    
    def calculate_gpa(self, courses: List[CourseGrade]) -> float:
        """
        计算GPA
        
        Args:
            courses: 课程成绩列表
            
        Returns:
            GPA
        """
        if not courses:
            return 0.0
        
        total_credits = sum(course.credit for course in courses)
        weighted_sum = sum(course.grade * course.credit for course in courses)
        
        return weighted_sum / total_credits if total_credits > 0 else 0.0
    
    def validate_and_correct_transcript(self, transcript: TranscriptData) -> TranscriptData:
        """
        验证和修正成绩单数据
        
        Args:
            transcript: 原始成绩单数据
            
        Returns:
            修正后的成绩单数据
        """
        corrected_courses = []
        
        for course in transcript.courses:
            # 验证成绩范围
            if 0 <= course.grade <= 4.0 and course.credit > 0:
                corrected_courses.append(course)
        
        # 重新计算GPA
        overall_gpa = self.calculate_gpa(corrected_courses)
        
        return TranscriptData(
            courses=corrected_courses,
            overall_gpa=overall_gpa
        )
