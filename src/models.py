"""
数据模型定义
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class DegreeLevel(str, Enum):
    """学位等级"""
    BACHELOR = "本科"
    MASTER = "硕士"
    PHD = "博士"


class InstitutionType(str, Enum):
    """院校类型"""
    C9 = "C9"
    UNIVERSITY_985 = "985"
    UNIVERSITY_211 = "211"
    DOUBLE_FIRST_CLASS = "双一流"
    REGULAR_UNDERGRADUATE = "普通本科"
    REGULAR_COLLEGE = "普通专科"


class Country(str, Enum):
    """目标国家"""
    USA = "美国"
    UK = "英国"
    CANADA = "加拿大"
    AUSTRALIA = "澳大利亚"
    SINGAPORE = "新加坡"
    GERMANY = "德国"
    FRANCE = "法国"
    JAPAN = "日本"
    SOUTH_KOREA = "韩国"


class ApplicationResult(str, Enum):
    """申请结果"""
    ADMITTED = "Admitted"
    REJECTED = "Rejected"
    WAITLISTED = "Waitlisted"


class UserProfile(BaseModel):
    """用户背景信息"""
    # 基本信息
    name: Optional[str] = None
    age: Optional[int] = None
    
    # 学术背景
    undergraduate_school: Optional[str] = None
    undergraduate_major: Optional[str] = None
    institution_type: Optional[InstitutionType] = None
    gpa: Optional[float] = None
    degree_year: Optional[int] = None
    
    # 语言成绩
    toefl_score: Optional[int] = None
    ielts_score: Optional[float] = None
    
    # 标准化考试
    gre_verbal: Optional[int] = None
    gre_quantitative: Optional[int] = None
    gre_writing: Optional[float] = None
    gmat_score: Optional[int] = None
    
    # 研究经历
    research_experience: Optional[bool] = False
    publications: Optional[List[str]] = []
    
    # 实习工作经历
    internships: Optional[List[str]] = []
    work_experience: Optional[List[str]] = []
    
    # 其他
    awards: Optional[List[str]] = []
    patents: Optional[List[str]] = []
    
    # 申请偏好
    target_countries: Optional[List[Country]] = []
    target_majors: Optional[List[str]] = []
    preferred_term: Optional[str] = None


class CourseGrade(BaseModel):
    """课程成绩"""
    course_name: str
    grade: float
    credit: float
    semester: Optional[str] = None


class TranscriptData(BaseModel):
    """成绩单数据"""
    courses: List[CourseGrade]
    overall_gpa: Optional[float] = None
    major_gpa: Optional[float] = None


class UniversityProgram(BaseModel):
    """大学专业信息"""
    university_id: str
    university_name: str
    university_name_cn: str
    program_name: str
    degree: str
    duration: str
    tuition_fee: str
    gpa_requirement: float
    language_requirement: str
    application_deadline: str
    qs_ranking: int
    official_website: str
    core_courses: List[str] = []


class RecommendationResult(BaseModel):
    """推荐结果"""
    program: UniversityProgram
    match_score: float
    match_type: str  # "冲刺", "匹配", "保底"
    recommendation_reason: str


class ApplicationDocument(BaseModel):
    """申请文书"""
    document_type: str  # "Personal Statement", "Statement of Purpose", etc.
    content: str
    target_program: UniversityProgram


class AgentState(BaseModel):
    """Agent状态"""
    # 用户信息
    user_profile: UserProfile = Field(default_factory=UserProfile)
    transcript_data: Optional[TranscriptData] = None
    resume_text: Optional[str] = None
    
    # 推荐结果
    recommendations: List[RecommendationResult] = []
    selected_programs: List[UniversityProgram] = []
    
    # 生成的文书
    documents: List[ApplicationDocument] = []
    
    # 对话历史
    conversation_history: List[Dict[str, Any]] = []
    
    # 当前步骤
    current_step: str = "start"
    
    # 临时数据
    temp_data: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        arbitrary_types_allowed = True
