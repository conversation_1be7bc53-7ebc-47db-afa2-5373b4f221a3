"""
简化版申请文书生成模块（不依赖OpenAI API）
"""
from typing import Dict, Any, List, Optional
from models import UserProfile, UniversityProgram, ApplicationDocument


class SimpleDocumentGenerator:
    """简化版申请文书生成器"""
    
    def __init__(self):
        """初始化文书生成器"""
        pass
    
    def generate_personal_statement(
        self, 
        user_profile: UserProfile, 
        target_program: UniversityProgram,
        resume_text: Optional[str] = None
    ) -> ApplicationDocument:
        """
        生成个人陈述
        
        Args:
            user_profile: 用户背景信息
            target_program: 目标项目
            resume_text: 简历文本
            
        Returns:
            申请文书
        """
        # 构建个人陈述内容
        content = self._build_personal_statement(user_profile, target_program, resume_text)
        
        return ApplicationDocument(
            document_type="Personal Statement",
            content=content,
            target_program=target_program
        )
    
    def _build_personal_statement(
        self, 
        user_profile: UserProfile, 
        target_program: UniversityProgram,
        resume_text: Optional[str] = None
    ) -> str:
        """构建个人陈述内容"""
        
        # 开头段落
        opening = self._generate_opening(user_profile, target_program)
        
        # 学术背景段落
        academic_background = self._generate_academic_background(user_profile)
        
        # 研究/实践经历段落
        experience = self._generate_experience(user_profile, resume_text)
        
        # 职业目标段落
        career_goals = self._generate_career_goals(user_profile, target_program)
        
        # 为什么选择该校该专业
        why_this_program = self._generate_why_this_program(user_profile, target_program)
        
        # 结尾段落
        conclusion = self._generate_conclusion(user_profile, target_program)
        
        # 组合所有段落
        content = f"""Dear Admissions Committee,

{opening}

{academic_background}

{experience}

{career_goals}

{why_this_program}

{conclusion}

Sincerely,
{user_profile.name or "Applicant"}"""
        
        return content
    
    def _generate_opening(self, user_profile: UserProfile, target_program: UniversityProgram) -> str:
        """生成开头段落"""
        major_field = self._get_major_field(target_program.program_name)
        
        opening = f"I am writing to express my strong interest in pursuing a {target_program.degree} in {target_program.program_name} at {target_program.university_name}. "
        
        if user_profile.undergraduate_major:
            opening += f"With my background in {user_profile.undergraduate_major} from {user_profile.undergraduate_school or 'my undergraduate institution'}, "
        
        opening += f"I am eager to advance my knowledge and skills in {major_field} through your distinguished program."
        
        return opening
    
    def _generate_academic_background(self, user_profile: UserProfile) -> str:
        """生成学术背景段落"""
        background = "During my undergraduate studies, I have built a solid foundation in my field. "
        
        if user_profile.gpa:
            if user_profile.gpa >= 3.5:
                background += f"I have maintained excellent academic performance with a GPA of {user_profile.gpa}, "
            else:
                background += f"Through consistent effort, I have achieved a GPA of {user_profile.gpa}, "
        
        if user_profile.undergraduate_major:
            if "计算机" in user_profile.undergraduate_major or "Computer" in user_profile.undergraduate_major:
                background += "which has equipped me with strong analytical and problem-solving skills. I have gained proficiency in programming languages, data structures, algorithms, and software development methodologies."
            elif "工程" in user_profile.undergraduate_major or "Engineering" in user_profile.undergraduate_major:
                background += "which has provided me with a comprehensive understanding of engineering principles and practical problem-solving approaches."
            elif "管理" in user_profile.undergraduate_major or "Business" in user_profile.undergraduate_major:
                background += "which has given me insights into business operations, strategic thinking, and leadership principles."
            else:
                background += "which has provided me with a comprehensive understanding of the fundamental concepts and methodologies in my field."
        
        return background
    
    def _generate_experience(self, user_profile: UserProfile, resume_text: Optional[str] = None) -> str:
        """生成经历段落"""
        experience = ""
        
        # 研究经历
        if user_profile.research_experience:
            experience += "I have gained valuable research experience through various projects and initiatives. "
            if user_profile.publications:
                experience += f"My research efforts have resulted in {len(user_profile.publications)} publication(s), demonstrating my ability to contribute to academic knowledge. "
        
        # 实习经历
        if user_profile.internships:
            experience += f"My practical experience includes internships at {', '.join(user_profile.internships[:2])}{'and other organizations' if len(user_profile.internships) > 2 else ''}. "
            experience += "These experiences have enhanced my understanding of real-world applications and industry practices. "
        
        # 获奖情况
        if user_profile.awards:
            experience += f"I have been recognized for my achievements through various awards and honors, including {', '.join(user_profile.awards[:2])}{'and others' if len(user_profile.awards) > 2 else ''}. "
        
        # 如果没有特殊经历，使用通用描述
        if not experience:
            experience = "Throughout my academic journey, I have actively sought opportunities to apply theoretical knowledge to practical situations. I have participated in various projects and activities that have broadened my perspective and enhanced my skills."
        
        return experience
    
    def _generate_career_goals(self, user_profile: UserProfile, target_program: UniversityProgram) -> str:
        """生成职业目标段落"""
        major_field = self._get_major_field(target_program.program_name)
        
        goals = f"My long-term career goal is to become a leading professional in {major_field}. "
        
        if "Computer" in target_program.program_name or "AI" in target_program.program_name:
            goals += "I aspire to contribute to technological innovation and develop solutions that can make a positive impact on society. I am particularly interested in the intersection of technology and real-world problem-solving."
        elif "Business" in target_program.program_name or "Management" in target_program.program_name:
            goals += "I aim to take on leadership roles in multinational organizations, driving strategic initiatives and fostering innovation in the global business environment."
        elif "Engineering" in target_program.program_name:
            goals += "I want to work on cutting-edge engineering projects that address global challenges and contribute to sustainable development."
        else:
            goals += "I am committed to making meaningful contributions to my field and using my knowledge to address important challenges facing our society."
        
        goals += f" The {target_program.program_name} program at {target_program.university_name} will provide me with the advanced knowledge and skills necessary to achieve these goals."
        
        return goals
    
    def _generate_why_this_program(self, user_profile: UserProfile, target_program: UniversityProgram) -> str:
        """生成选择该项目的原因"""
        why = f"I am particularly drawn to {target_program.university_name} for several reasons. "
        
        # 排名优势
        if target_program.qs_ranking <= 20:
            why += f"As a top-tier institution ranked #{target_program.qs_ranking} globally in the QS World University Rankings, {target_program.university_name} represents academic excellence and innovation. "
        elif target_program.qs_ranking <= 50:
            why += f"With its strong global reputation (QS ranking #{target_program.qs_ranking}), {target_program.university_name} offers world-class education and research opportunities. "
        
        # 项目特色
        why += f"The {target_program.program_name} program aligns perfectly with my academic interests and career aspirations. "
        
        # 课程优势
        if target_program.core_courses:
            why += f"I am particularly excited about courses such as {', '.join(target_program.core_courses[:2])}, which will deepen my understanding of key concepts in the field. "
        
        # 国际化环境
        why += "The diverse and international learning environment will provide me with global perspectives and valuable networking opportunities that are essential for my future career."
        
        return why
    
    def _generate_conclusion(self, user_profile: UserProfile, target_program: UniversityProgram) -> str:
        """生成结尾段落"""
        conclusion = f"I am confident that my academic background, practical experience, and passion for {self._get_major_field(target_program.program_name)} make me a strong candidate for the {target_program.program_name} program. "
        conclusion += f"I am excited about the opportunity to contribute to the vibrant academic community at {target_program.university_name} and to grow both personally and professionally through this transformative experience. "
        conclusion += "Thank you for considering my application. I look forward to the opportunity to discuss my qualifications further."
        
        return conclusion
    
    def _get_major_field(self, program_name: str) -> str:
        """获取专业领域"""
        if "Computer" in program_name or "AI" in program_name or "Data" in program_name:
            return "computer science and technology"
        elif "Business" in program_name or "Management" in program_name:
            return "business and management"
        elif "Engineering" in program_name:
            return "engineering"
        elif "Economics" in program_name or "Finance" in program_name:
            return "economics and finance"
        elif "Medicine" in program_name or "Health" in program_name:
            return "healthcare and medicine"
        else:
            return "my chosen field"
    
    def generate_statement_of_purpose(
        self,
        user_profile: UserProfile,
        target_program: UniversityProgram,
        research_interests: str = "",
        career_goals: str = ""
    ) -> ApplicationDocument:
        """
        生成目的陈述
        
        Args:
            user_profile: 用户背景信息
            target_program: 目标项目
            research_interests: 研究兴趣
            career_goals: 职业目标
            
        Returns:
            申请文书
        """
        content = f"""Statement of Purpose

I am applying for the {target_program.program_name} program at {target_program.university_name} to advance my knowledge and research capabilities in {self._get_major_field(target_program.program_name)}.

Academic Background:
My undergraduate education in {user_profile.undergraduate_major or 'my field'} at {user_profile.undergraduate_school or 'my university'} has provided me with a strong foundation. With a GPA of {user_profile.gpa or 'strong academic performance'}, I have demonstrated my commitment to academic excellence.

Research Interests:
{research_interests or self._generate_default_research_interests(target_program)}

Career Goals:
{career_goals or self._generate_default_career_goals(target_program)}

Why This Program:
The {target_program.program_name} program at {target_program.university_name} is ideally suited to help me achieve my academic and professional goals. The program's emphasis on research and innovation, combined with the university's world-class faculty and resources, makes it the perfect environment for my graduate studies.

I am excited about the opportunity to contribute to ongoing research projects and to work with distinguished faculty members who share my research interests.

{user_profile.name or 'Applicant'}"""
        
        return ApplicationDocument(
            document_type="Statement of Purpose",
            content=content,
            target_program=target_program
        )
    
    def _generate_default_research_interests(self, target_program: UniversityProgram) -> str:
        """生成默认研究兴趣"""
        if "Computer" in target_program.program_name or "AI" in target_program.program_name:
            return "I am particularly interested in machine learning, artificial intelligence, and their applications in solving real-world problems. I want to explore how these technologies can be used to improve efficiency and create innovative solutions."
        elif "Engineering" in target_program.program_name:
            return "My research interests lie in the intersection of engineering principles and emerging technologies. I am eager to work on projects that address sustainability and technological advancement."
        else:
            return f"I am passionate about advancing knowledge in {self._get_major_field(target_program.program_name)} through rigorous research and innovative approaches."
    
    def _generate_default_career_goals(self, target_program: UniversityProgram) -> str:
        """生成默认职业目标"""
        if "Computer" in target_program.program_name or "AI" in target_program.program_name:
            return "Upon completion of my graduate studies, I plan to pursue a career in technology research and development, either in industry or academia. I aim to contribute to technological innovation and help bridge the gap between theoretical research and practical applications."
        elif "Business" in target_program.program_name:
            return "My goal is to become a strategic leader in the global business environment, leveraging my graduate education to drive innovation and growth in multinational organizations."
        else:
            return f"I aspire to become a leader in {self._get_major_field(target_program.program_name)}, contributing to both academic research and practical applications that benefit society."
