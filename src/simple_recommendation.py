"""
简化版推荐引擎（不依赖复杂的ML库）
"""
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import re

from models import UserProfile, UniversityProgram, RecommendationResult, InstitutionType, Country
from data_loader import data_loader


class SimpleRecommendationEngine:
    """简化版推荐引擎"""
    
    def __init__(self):
        """初始化推荐引擎"""
        # 院校权重映射
        self.institution_weights = {
            InstitutionType.C9: 1.0,
            InstitutionType.UNIVERSITY_985: 0.95,
            InstitutionType.UNIVERSITY_211: 0.85,
            InstitutionType.DOUBLE_FIRST_CLASS: 0.80,
            InstitutionType.REGULAR_UNDERGRADUATE: 0.70,
            InstitutionType.REGULAR_COLLEGE: 0.60
        }
        
        # 国家映射
        self.country_mapping = {
            Country.USA: "United States",
            Country.UK: "United Kingdom", 
            Country.CANADA: "Canada",
            Country.AUSTRALIA: "Australia",
            Country.SINGAPORE: "Singapore",
            Country.GERMANY: "Germany",
            Country.FRANCE: "France",
            Country.JAPAN: "Japan",
            Country.SOUTH_KOREA: "South Korea"
        }
    
    def calculate_match_score(self, user_profile: UserProfile, program: Dict[str, Any]) -> float:
        """
        计算匹配分数
        
        Args:
            user_profile: 用户背景信息
            program: 大学专业信息
            
        Returns:
            匹配分数 (0-100)
        """
        score = 0.0
        
        # 1. GPA分数 (30%)
        if user_profile.gpa and program.get('gpa_requirement'):
            gpa_score = self._calculate_gpa_score(user_profile.gpa, program['gpa_requirement'])
            institution_weight = self.institution_weights.get(user_profile.institution_type, 0.7)
            score += 30 * gpa_score * institution_weight
        else:
            score += 30 * 0.7  # 默认分数
        
        # 2. 院校声誉分数 (10%)
        reputation_score = self._calculate_reputation_score(user_profile.undergraduate_school)
        score += 10 * reputation_score
        
        # 3. 专业相关度 (15%)
        major_score = self._calculate_major_similarity_simple(
            user_profile.undergraduate_major, 
            program.get('program_name', ''),
            user_profile.target_majors or []
        )
        score += 15 * major_score
        
        # 4. 语言成绩 (10%)
        language_score = self._calculate_language_score(user_profile, program.get('language_req', ''))
        score += 10 * language_score
        
        # 5. 标准化考试 (10%)
        test_score = self._calculate_test_score(user_profile)
        score += 10 * test_score
        
        # 6. 研究经历 (10%)
        research_score = self._calculate_research_score(user_profile)
        score += 10 * research_score
        
        # 7. 课外活动/奖项 (5%)
        activity_score = self._calculate_activity_score(user_profile)
        score += 5 * activity_score
        
        # 8. 实习/工作经历 (5%)
        work_score = self._calculate_work_score(user_profile)
        score += 5 * work_score
        
        # 9. 推荐信 (5%)
        recommendation_score = 0.7  # 默认分数
        score += 5 * recommendation_score
        
        return min(score, 100.0)
    
    def _calculate_gpa_score(self, user_gpa: float, required_gpa: float) -> float:
        """计算GPA分数"""
        if user_gpa >= required_gpa + 0.3:
            return 1.0
        elif user_gpa >= required_gpa:
            return 0.8
        elif user_gpa >= required_gpa - 0.2:
            return 0.6
        elif user_gpa >= required_gpa - 0.4:
            return 0.4
        else:
            return 0.2
    
    def _calculate_reputation_score(self, school_name: str) -> float:
        """计算院校声誉分数"""
        if not school_name:
            return 0.5
        
        # C9联盟
        c9_schools = ['清华', '北大', '复旦', '上交', '浙大', '中科大', '南大', '哈工大', '西交']
        if any(keyword in school_name for keyword in c9_schools):
            return 1.0
        
        # 985院校关键词
        prestigious_985 = ['北京理工', '北航', '同济', '华科', '中南', '东南', '天大', '大连理工', '西工大']
        if any(keyword in school_name for keyword in prestigious_985):
            return 0.9
        
        # 211院校关键词
        good_211 = ['北京交通', '北京科技', '华北电力', '北京邮电', '对外经贸', '中央财经']
        if any(keyword in school_name for keyword in good_211):
            return 0.8
        
        # 双一流
        if '大学' in school_name:
            return 0.7
        
        return 0.6
    
    def _calculate_major_similarity_simple(self, user_major: str, program_name: str, target_majors: List[str]) -> float:
        """简单计算专业相关度"""
        if not user_major or not program_name:
            return 0.5
        
        # 转换为小写进行比较
        user_major_lower = user_major.lower()
        program_name_lower = program_name.lower()
        target_majors_lower = [m.lower() for m in target_majors]
        
        # 计算关键词匹配
        score = 0.0
        
        # 计算机相关专业
        cs_keywords = ['计算机', 'computer', '软件', 'software', '人工智能', 'ai', 'artificial', '数据', 'data']
        if any(kw in user_major_lower for kw in cs_keywords):
            if any(kw in program_name_lower for kw in cs_keywords):
                score += 0.4
        
        # 工程类专业
        eng_keywords = ['工程', 'engineering', '电子', 'electronic', '机械', 'mechanical', '自动化', 'automation']
        if any(kw in user_major_lower for kw in eng_keywords):
            if any(kw in program_name_lower for kw in eng_keywords):
                score += 0.3
        
        # 商科专业
        business_keywords = ['管理', 'management', '商务', 'business', '金融', 'finance', '经济', 'economics']
        if any(kw in user_major_lower for kw in business_keywords):
            if any(kw in program_name_lower for kw in business_keywords):
                score += 0.3
        
        # 目标专业匹配
        for target in target_majors_lower:
            if target in program_name_lower:
                score += 0.3
                break
        
        return min(score, 1.0)
    
    def _calculate_language_score(self, user_profile: UserProfile, requirement: str) -> float:
        """计算语言成绩分数"""
        if not requirement:
            return 0.7
        
        # 解析语言要求
        if 'IELTS' in requirement.upper():
            required_score = self._extract_ielts_score(requirement)
            if user_profile.ielts_score:
                if user_profile.ielts_score >= required_score + 0.5:
                    return 1.0
                elif user_profile.ielts_score >= required_score:
                    return 0.8
                else:
                    return 0.4
        elif 'TOEFL' in requirement.upper():
            required_score = self._extract_toefl_score(requirement)
            if user_profile.toefl_score:
                if user_profile.toefl_score >= required_score + 10:
                    return 1.0
                elif user_profile.toefl_score >= required_score:
                    return 0.8
                else:
                    return 0.4
        
        return 0.5
    
    def _extract_ielts_score(self, requirement: str) -> float:
        """从要求中提取雅思分数"""
        match = re.search(r'IELTS\s*(\d+\.?\d*)', requirement.upper())
        return float(match.group(1)) if match else 6.5
    
    def _extract_toefl_score(self, requirement: str) -> int:
        """从要求中提取托福分数"""
        match = re.search(r'TOEFL\s*(\d+)', requirement.upper())
        return int(match.group(1)) if match else 90
    
    def _calculate_test_score(self, user_profile: UserProfile) -> float:
        """计算标准化考试分数"""
        score = 0.0
        
        if user_profile.gre_verbal and user_profile.gre_quantitative:
            verbal_score = min(user_profile.gre_verbal / 170, 1.0)
            quant_score = min(user_profile.gre_quantitative / 170, 1.0)
            score = (verbal_score + quant_score) / 2
        elif user_profile.gmat_score:
            score = min(user_profile.gmat_score / 800, 1.0)
        else:
            score = 0.5
        
        return score
    
    def _calculate_research_score(self, user_profile: UserProfile) -> float:
        """计算研究经历分数"""
        score = 0.0
        
        if user_profile.research_experience:
            score += 0.5
        
        if user_profile.publications:
            score += min(len(user_profile.publications) * 0.2, 0.5)
        
        return min(score, 1.0)
    
    def _calculate_activity_score(self, user_profile: UserProfile) -> float:
        """计算课外活动分数"""
        score = 0.0
        
        if user_profile.awards:
            score += min(len(user_profile.awards) * 0.2, 0.6)
        
        if user_profile.patents:
            score += min(len(user_profile.patents) * 0.3, 0.4)
        
        return min(score, 1.0)
    
    def _calculate_work_score(self, user_profile: UserProfile) -> float:
        """计算工作经历分数"""
        score = 0.0
        
        if user_profile.internships:
            prestigious_companies = ['Google', 'Microsoft', '腾讯', '阿里巴巴', '字节跳动', '华为', '百度', '美团', '京东']
            for internship in user_profile.internships:
                if any(company in internship for company in prestigious_companies):
                    score += 0.4
                else:
                    score += 0.2
        
        if user_profile.work_experience:
            score += min(len(user_profile.work_experience) * 0.3, 0.4)
        
        return min(score, 1.0)
    
    def get_recommendations(self, user_profile: UserProfile, top_k: int = 20) -> List[RecommendationResult]:
        """
        获取推荐结果
        
        Args:
            user_profile: 用户背景信息
            top_k: 返回推荐数量
            
        Returns:
            推荐结果列表
        """
        # 1. 根据目标国家筛选大学
        if user_profile.target_countries:
            target_country_names = [self.country_mapping.get(country, country.value) 
                                  for country in user_profile.target_countries]
            universities = data_loader.get_universities_by_country(target_country_names)
        else:
            universities = data_loader.universities_df
        
        # 2. 根据目标专业筛选专业
        if user_profile.target_majors:
            programs = data_loader.get_programs_by_major_keywords(user_profile.target_majors)
        else:
            programs = data_loader.programs_df
        
        # 3. 合并大学和专业信息
        merged_data = programs.merge(universities, on='university_id', how='inner')
        
        if len(merged_data) == 0:
            return []
        
        # 4. 计算匹配分数
        recommendations = []
        for _, row in merged_data.iterrows():
            match_score = self.calculate_match_score(user_profile, row.to_dict())
            
            # 确定匹配类型
            if match_score >= 85:
                match_type = "冲刺"
            elif match_score >= 70:
                match_type = "匹配"
            else:
                match_type = "保底"
            
            # 获取核心课程
            core_courses = self._get_core_courses(row['program_id'])
            
            program = UniversityProgram(
                university_id=str(row['university_id']),
                university_name=str(row['university_name']),
                university_name_cn=str(row['university_name_cn']),
                program_name=str(row['program_name']),
                degree=str(row['degree']),
                duration=str(row['duration']),
                tuition_fee=str(row['intl_student_fee']),
                gpa_requirement=float(row['gpa_requirement']),
                language_requirement=str(row['language_req']),
                application_deadline=str(row['application_deadline']),
                qs_ranking=int(row['qs_ranking']),
                official_website=str(row['official_website']),
                core_courses=core_courses
            )
            
            recommendation = RecommendationResult(
                program=program,
                match_score=match_score,
                match_type=match_type,
                recommendation_reason=self._generate_recommendation_reason(user_profile, program, match_score)
            )
            
            recommendations.append(recommendation)
        
        # 5. 排序并返回前top_k个
        recommendations.sort(key=lambda x: x.match_score, reverse=True)
        
        # 6. 确保推荐分布合理
        return self._balance_recommendations(recommendations[:top_k])
    
    def _get_core_courses(self, program_id: str) -> List[str]:
        """获取专业核心课程"""
        try:
            courses = data_loader.get_program_courses(program_id)
            core_courses = [course['course_name'] for course in courses 
                           if course['course_type'] == '必修课'][:5]
            return core_courses
        except:
            return []
    
    def _generate_recommendation_reason(self, user_profile: UserProfile, program: UniversityProgram, score: float) -> str:
        """生成推荐理由"""
        reasons = []
        
        if score >= 85:
            reasons.append(f"您的背景与{program.university_name_cn}的{program.program_name}专业高度匹配")
        elif score >= 70:
            reasons.append(f"您的条件符合{program.university_name_cn}的录取要求")
        else:
            reasons.append(f"{program.university_name_cn}可作为保底选择")
        
        if user_profile.gpa and user_profile.gpa >= program.gpa_requirement:
            reasons.append(f"您的GPA {user_profile.gpa} 满足要求")
        
        if program.qs_ranking <= 50:
            reasons.append(f"QS世界排名第{program.qs_ranking}位，声誉卓著")
        
        return "；".join(reasons)
    
    def _balance_recommendations(self, recommendations: List[RecommendationResult]) -> List[RecommendationResult]:
        """平衡推荐结果分布"""
        reach_schools = [r for r in recommendations if r.match_type == "冲刺"][:4]
        match_schools = [r for r in recommendations if r.match_type == "匹配"][:10]
        safety_schools = [r for r in recommendations if r.match_type == "保底"][:6]
        
        balanced = reach_schools + match_schools + safety_schools
        return balanced[:20]
