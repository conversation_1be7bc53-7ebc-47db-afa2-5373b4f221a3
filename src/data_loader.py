"""
数据加载和管理模块
"""
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
import os
from pathlib import Path


class DataLoader:
    """数据加载器"""
    
    def __init__(self, data_dir: str = "."):
        """
        初始化数据加载器
        
        Args:
            data_dir: 数据文件目录
        """
        self.data_dir = Path(data_dir)
        self._universities_df = None
        self._colleges_df = None
        self._departments_df = None
        self._programs_df = None
        self._courses_df = None
        self._course_selection_df = None
        self._applications_df = None
    
    @property
    def universities_df(self) -> pd.DataFrame:
        """获取大学数据"""
        if self._universities_df is None:
            self._universities_df = pd.read_csv(self.data_dir / "universities.csv")
        return self._universities_df
    
    @property
    def colleges_df(self) -> pd.DataFrame:
        """获取学院数据"""
        if self._colleges_df is None:
            self._colleges_df = pd.read_csv(self.data_dir / "college.csv")
        return self._colleges_df
    
    @property
    def departments_df(self) -> pd.DataFrame:
        """获取系数据"""
        if self._departments_df is None:
            self._departments_df = pd.read_csv(self.data_dir / "department.csv")
        return self._departments_df
    
    @property
    def programs_df(self) -> pd.DataFrame:
        """获取专业数据"""
        if self._programs_df is None:
            self._programs_df = pd.read_csv(self.data_dir / "degree_programs.csv")
        return self._programs_df
    
    @property
    def courses_df(self) -> pd.DataFrame:
        """获取课程数据"""
        if self._courses_df is None:
            self._courses_df = pd.read_csv(self.data_dir / "courses.csv")
        return self._courses_df
    
    @property
    def course_selection_df(self) -> pd.DataFrame:
        """获取选课数据"""
        if self._course_selection_df is None:
            self._course_selection_df = pd.read_csv(self.data_dir / "course_selection.csv")
        return self._course_selection_df
    
    @property
    def applications_df(self) -> pd.DataFrame:
        """获取申请历史数据"""
        if self._applications_df is None:
            self._applications_df = pd.read_csv(self.data_dir / "graduate_application.csv")
        return self._applications_df
    
    def get_universities_by_country(self, countries: List[str]) -> pd.DataFrame:
        """
        根据国家筛选大学
        
        Args:
            countries: 国家列表
            
        Returns:
            筛选后的大学数据
        """
        return self.universities_df[self.universities_df['country'].isin(countries)]
    
    def get_programs_by_university(self, university_id: str) -> pd.DataFrame:
        """
        获取指定大学的所有专业
        
        Args:
            university_id: 大学ID
            
        Returns:
            专业数据
        """
        return self.programs_df[self.programs_df['university_id'] == university_id]
    
    def get_programs_by_major_keywords(self, keywords: List[str]) -> pd.DataFrame:
        """
        根据专业关键词搜索专业
        
        Args:
            keywords: 关键词列表
            
        Returns:
            匹配的专业数据
        """
        pattern = '|'.join(keywords)
        mask = self.programs_df['program_name'].str.contains(pattern, case=False, na=False)
        return self.programs_df[mask]
    
    def get_similar_applications(self, user_profile: Dict[str, Any], top_k: int = 10) -> pd.DataFrame:
        """
        获取相似的申请案例
        
        Args:
            user_profile: 用户背景信息
            top_k: 返回前k个相似案例
            
        Returns:
            相似申请案例
        """
        # 简单的相似度计算，可以后续优化
        apps_df = self.applications_df.copy()
        
        # 计算GPA相似度
        if 'gpa' in user_profile and user_profile['gpa']:
            apps_df['gpa_similarity'] = 1 - abs(apps_df['gpa'] - user_profile['gpa']) / 4.0
        else:
            apps_df['gpa_similarity'] = 0.5
        
        # 计算专业相似度（简化版）
        if 'major' in user_profile and user_profile['major']:
            apps_df['major_similarity'] = apps_df['major'].str.contains(
                user_profile['major'], case=False, na=False
            ).astype(float)
        else:
            apps_df['major_similarity'] = 0.5
        
        # 计算院校类型相似度
        if 'institution_type' in user_profile and user_profile['institution_type']:
            apps_df['institution_similarity'] = apps_df['institution_type'].str.contains(
                user_profile['institution_type'], case=False, na=False
            ).astype(float)
        else:
            apps_df['institution_similarity'] = 0.5
        
        # 综合相似度
        apps_df['total_similarity'] = (
            apps_df['gpa_similarity'] * 0.4 +
            apps_df['major_similarity'] * 0.3 +
            apps_df['institution_similarity'] * 0.3
        )
        
        return apps_df.nlargest(top_k, 'total_similarity')
    
    def get_program_courses(self, program_id: str) -> List[Dict[str, Any]]:
        """
        获取专业的课程信息
        
        Args:
            program_id: 专业ID
            
        Returns:
            课程列表
        """
        # 获取该专业的选课记录
        course_selections = self.course_selection_df[
            self.course_selection_df['program_id'] == program_id
        ]
        
        # 获取课程详细信息
        course_ids = course_selections['course_id'].tolist()
        courses = self.courses_df[self.courses_df['course_id'].isin(course_ids)]
        
        # 合并课程类型信息
        result = []
        for _, course in courses.iterrows():
            course_selection = course_selections[
                course_selections['course_id'] == course['course_id']
            ].iloc[0]
            
            result.append({
                'course_name': course['course_name'],
                'course_code': course['course_code'],
                'credit': course['credit'],
                'course_type': course_selection['course_type'],
                'prerequisite': course['prerequisite']
            })
        
        return result


# 全局数据加载器实例
data_loader = DataLoader()
