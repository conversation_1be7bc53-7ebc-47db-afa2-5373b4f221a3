"""
测试选择项目功能
"""
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from models import UserProfile, InstitutionType, Country
from simple_recommendation import SimpleRecommendationEngine

def test_select_programs_logic():
    """测试选择项目的逻辑"""
    print("🧪 测试选择项目功能...")
    
    # 创建测试用户档案
    profile = UserProfile(
        name="测试用户",
        undergraduate_school="清华大学",
        undergraduate_major="计算机科学与技术",
        institution_type=InstitutionType.C9,
        gpa=3.7,
        target_countries=[Country.USA, Country.UK],
        target_majors=["计算机科学", "人工智能"]
    )
    
    print(f"👤 测试用户: {profile.name}")
    print(f"🎓 院校: {profile.undergraduate_school}")
    print(f"📊 GPA: {profile.gpa}")
    
    # 生成推荐
    engine = SimpleRecommendationEngine()
    recommendations = engine.get_recommendations(profile, top_k=10)
    
    print(f"\n🎯 生成推荐: {len(recommendations)} 个项目")
    
    if not recommendations:
        print("❌ 没有生成推荐结果")
        return False
    
    # 按类型分组
    reach_schools = [r for r in recommendations if r.match_type == "冲刺"]
    match_schools = [r for r in recommendations if r.match_type == "匹配"]
    safety_schools = [r for r in recommendations if r.match_type == "保底"]
    
    print(f"🚀 冲刺院校: {len(reach_schools)} 所")
    print(f"🎯 匹配院校: {len(match_schools)} 所")
    print(f"🛡️ 保底院校: {len(safety_schools)} 所")
    
    # 显示推荐详情
    print("\n📋 推荐详情:")
    for i, rec in enumerate(recommendations[:5], 1):
        print(f"  {i}. {rec.program.university_name_cn} - {rec.program.program_name}")
        print(f"     类型: {rec.match_type} | 匹配度: {rec.match_score:.1f}%")
        print(f"     QS排名: #{rec.program.qs_ranking}")
    
    if len(recommendations) > 5:
        print(f"     ... 还有 {len(recommendations) - 5} 个推荐")
    
    print("\n✅ 选择项目功能测试通过！")
    print("💡 用户现在应该能够看到所有推荐项目并进行选择")
    
    return True

def show_fix_info():
    """显示修复信息"""
    print("🔧 选择项目页面修复说明")
    print("=" * 40)
    print()
    print("🐛 问题:")
    print("  • 选择项目页面没有显示可选的推荐项目")
    print("  • 用户无法看到生成的推荐结果")
    print()
    print("✅ 修复:")
    print("  • 添加了推荐结果检查逻辑")
    print("  • 按类型分组显示项目（冲刺/匹配/保底）")
    print("  • 显示推荐统计信息")
    print("  • 添加了匹配度显示")
    print("  • 改进了选择界面的用户体验")
    print("  • 添加了返回按钮和错误处理")
    print()
    print("🎯 新功能:")
    print("  • 分类显示推荐项目")
    print("  • 实时显示选择统计")
    print("  • 更清晰的项目信息展示")
    print("  • 改进的导航体验")
    print()

if __name__ == "__main__":
    show_fix_info()
    success = test_select_programs_logic()
    
    if success:
        print("\n🎉 修复验证成功！")
        print("📱 请刷新浏览器页面查看修复效果")
    else:
        print("\n❌ 修复验证失败")
