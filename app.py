"""
留学申请匹配Agent - Streamlit应用
"""
import streamlit as st
import os
from dotenv import load_dotenv
import pandas as pd
from typing import Dict, Any, List
import json

from src.models import AgentState, UserProfile
from src.agent_graph import StudyAbroadAgent
from src.data_loader import data_loader

# 加载环境变量
load_dotenv()

# 页面配置
st.set_page_config(
    page_title="留学申请匹配Agent",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        border-left: 4px solid #1f77b4;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left-color: #2196f3;
    }
    .assistant-message {
        background-color: #f5f5f5;
        border-left-color: #4caf50;
    }
    .recommendation-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        background-color: #fafafa;
    }
    .match-score {
        font-weight: bold;
        color: #1f77b4;
    }
    .match-type-reach {
        color: #ff5722;
        font-weight: bold;
    }
    .match-type-match {
        color: #4caf50;
        font-weight: bold;
    }
    .match-type-safety {
        color: #2196f3;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """初始化会话状态"""
    if "agent_state" not in st.session_state:
        st.session_state.agent_state = AgentState()
    
    if "agent" not in st.session_state:
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            st.error("请设置OPENAI_API_KEY环境变量")
            st.stop()
        st.session_state.agent = StudyAbroadAgent(api_key)
    
    if "conversation_started" not in st.session_state:
        st.session_state.conversation_started = False


def display_chat_message(role: str, content: str):
    """显示聊天消息"""
    if role == "user":
        st.markdown(f"""
        <div class="chat-message user-message">
            <strong>👤 您:</strong><br>
            {content}
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown(f"""
        <div class="chat-message assistant-message">
            <strong>🤖 留学顾问:</strong><br>
            {content}
        </div>
        """, unsafe_allow_html=True)


def display_recommendations(recommendations: List):
    """显示推荐结果"""
    if not recommendations:
        return
    
    st.markdown("### 🎓 推荐结果")
    
    # 按匹配类型分组
    reach_schools = [r for r in recommendations if r.match_type == "冲刺"]
    match_schools = [r for r in recommendations if r.match_type == "匹配"]
    safety_schools = [r for r in recommendations if r.match_type == "保底"]
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["🚀 冲刺院校", "🎯 匹配院校", "🛡️ 保底院校", "📊 数据表格"])
    
    with tab1:
        if reach_schools:
            for i, rec in enumerate(reach_schools, 1):
                display_recommendation_card(i, rec)
        else:
            st.info("暂无冲刺院校推荐")
    
    with tab2:
        if match_schools:
            for i, rec in enumerate(match_schools, len(reach_schools) + 1):
                display_recommendation_card(i, rec)
        else:
            st.info("暂无匹配院校推荐")
    
    with tab3:
        if safety_schools:
            for i, rec in enumerate(safety_schools, len(reach_schools) + len(match_schools) + 1):
                display_recommendation_card(i, rec)
        else:
            st.info("暂无保底院校推荐")
    
    with tab4:
        # 创建数据表格
        df_data = []
        for i, rec in enumerate(recommendations, 1):
            df_data.append({
                "编号": i,
                "大学": rec.program.university_name_cn,
                "专业": rec.program.program_name,
                "QS排名": rec.program.qs_ranking,
                "学制": rec.program.duration,
                "学费": rec.program.tuition_fee,
                "GPA要求": rec.program.gpa_requirement,
                "语言要求": rec.program.language_requirement,
                "申请截止": rec.program.application_deadline,
                "匹配度": f"{rec.match_score:.1f}%",
                "类型": rec.match_type
            })
        
        df = pd.DataFrame(df_data)
        st.dataframe(df, use_container_width=True)


def display_recommendation_card(index: int, rec):
    """显示单个推荐卡片"""
    match_type_class = f"match-type-{rec.match_type.lower()}"
    
    st.markdown(f"""
    <div class="recommendation-card">
        <h4>{index}. {rec.program.university_name_cn} 
            <span class="match-score">({rec.match_score:.1f}%)</span>
            <span class="{match_type_class}">[{rec.match_type}]</span>
        </h4>
        <p><strong>🏫 QS排名:</strong> #{rec.program.qs_ranking}</p>
        <p><strong>📚 专业:</strong> {rec.program.program_name}</p>
        <p><strong>⏱️ 学制:</strong> {rec.program.duration}</p>
        <p><strong>💰 学费:</strong> {rec.program.tuition_fee}</p>
        <p><strong>📊 GPA要求:</strong> {rec.program.gpa_requirement}</p>
        <p><strong>🗣️ 语言要求:</strong> {rec.program.language_requirement}</p>
        <p><strong>📅 申请截止:</strong> {rec.program.application_deadline}</p>
        <p><strong>💡 推荐理由:</strong> {rec.recommendation_reason}</p>
        <p><strong>🔗 官网:</strong> <a href="{rec.program.official_website}" target="_blank">查看详情</a></p>
    </div>
    """, unsafe_allow_html=True)


def display_user_profile(profile: UserProfile):
    """显示用户档案"""
    st.markdown("### 👤 您的背景信息")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**基本信息**")
        if profile.name:
            st.write(f"姓名: {profile.name}")
        if profile.age:
            st.write(f"年龄: {profile.age}")
        if profile.undergraduate_school:
            st.write(f"本科院校: {profile.undergraduate_school}")
        if profile.undergraduate_major:
            st.write(f"本科专业: {profile.undergraduate_major}")
        if profile.institution_type:
            st.write(f"院校类型: {profile.institution_type}")
        if profile.gpa:
            st.write(f"GPA: {profile.gpa}")
    
    with col2:
        st.markdown("**申请偏好**")
        if profile.target_countries:
            st.write(f"目标国家: {', '.join(profile.target_countries)}")
        if profile.target_majors:
            st.write(f"目标专业: {', '.join(profile.target_majors)}")
        if profile.preferred_term:
            st.write(f"入学时间: {profile.preferred_term}")
        
        st.markdown("**语言成绩**")
        if profile.toefl_score:
            st.write(f"TOEFL: {profile.toefl_score}")
        if profile.ielts_score:
            st.write(f"IELTS: {profile.ielts_score}")


def display_documents(documents: List):
    """显示申请文书"""
    if not documents:
        return
    
    st.markdown("### 📝 申请文书")
    
    for i, doc in enumerate(documents):
        with st.expander(f"{doc.target_program.university_name_cn} - {doc.document_type}"):
            st.markdown(f"**目标项目:** {doc.target_program.program_name}")
            st.markdown("**文书内容:**")
            st.text_area(
                "内容",
                value=doc.content,
                height=400,
                key=f"doc_{i}",
                help="您可以编辑和完善这份文书"
            )
            
            # 下载按钮
            st.download_button(
                label="📥 下载文书",
                data=doc.content,
                file_name=f"{doc.target_program.university_name_cn}_{doc.document_type}.txt",
                mime="text/plain"
            )


def main():
    """主函数"""
    initialize_session_state()
    
    # 页面标题
    st.markdown('<h1 class="main-header">🎓 留学申请匹配Agent</h1>', unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.markdown("### 📊 数据统计")
        st.metric("大学数量", len(data_loader.universities_df))
        st.metric("专业数量", len(data_loader.programs_df))
        st.metric("申请案例", len(data_loader.applications_df))
        
        st.markdown("### ⚙️ 设置")
        if st.button("🔄 重新开始"):
            st.session_state.agent_state = AgentState()
            st.session_state.conversation_started = False
            st.rerun()
        
        # 显示用户档案
        if st.session_state.agent_state.user_profile.name:
            display_user_profile(st.session_state.agent_state.user_profile)
    
    # 主要内容区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("### 💬 对话")
        
        # 显示对话历史
        chat_container = st.container()
        with chat_container:
            for message in st.session_state.agent_state.conversation_history:
                display_chat_message(message["role"], message["content"])
        
        # 开始对话
        if not st.session_state.conversation_started:
            if st.button("🚀 开始留学规划", type="primary"):
                # 启动Agent
                st.session_state.agent_state = st.session_state.agent.start_conversation(
                    st.session_state.agent_state
                )
                st.session_state.conversation_started = True
                st.rerun()
        
        # 用户输入
        if st.session_state.conversation_started:
            user_input = st.chat_input("请输入您的信息...")
            
            if user_input:
                # 处理用户输入
                st.session_state.agent_state = st.session_state.agent.process_user_input(
                    st.session_state.agent_state, user_input
                )
                
                # 运行Agent工作流
                if st.session_state.agent_state.current_step != "completed":
                    try:
                        result = st.session_state.agent.graph.invoke(st.session_state.agent_state)
                        st.session_state.agent_state = result
                    except Exception as e:
                        st.error(f"处理过程中出现错误: {e}")
                
                st.rerun()
    
    with col2:
        st.markdown("### 📋 进度")
        
        # 显示当前步骤
        steps = [
            ("start", "开始"),
            ("collect_basic_info", "收集基本信息"),
            ("process_transcript", "处理成绩单"),
            ("collect_preferences", "收集偏好"),
            ("generate_recommendations", "生成推荐"),
            ("select_programs", "选择项目"),
            ("process_resume", "处理简历"),
            ("generate_documents", "生成文书"),
            ("completed", "完成")
        ]
        
        current_step = st.session_state.agent_state.current_step
        for step_id, step_name in steps:
            if step_id == current_step:
                st.markdown(f"🔄 **{step_name}** (当前)")
            elif steps.index((step_id, step_name)) < steps.index((current_step, next(name for sid, name in steps if sid == current_step))):
                st.markdown(f"✅ {step_name}")
            else:
                st.markdown(f"⏳ {step_name}")
    
    # 显示推荐结果
    if st.session_state.agent_state.recommendations:
        display_recommendations(st.session_state.agent_state.recommendations)
    
    # 显示申请文书
    if st.session_state.agent_state.documents:
        display_documents(st.session_state.agent_state.documents)
    
    # 页脚
    st.markdown("---")
    st.markdown(
        "💡 **提示**: 这是一个AI驱动的留学申请助手，建议结果仅供参考，请结合实际情况做出决策。"
    )


if __name__ == "__main__":
    main()
