"""
重新加载Streamlit应用
"""
import time
import requests

def reload_streamlit_app():
    """重新加载Streamlit应用"""
    print("🔄 重新加载Streamlit应用...")
    
    try:
        # 发送请求触发重新加载
        response = requests.get("http://localhost:8501", timeout=5)
        print(f"✅ 应用响应正常，状态码: {response.status_code}")
        
        print("\n🎉 修复已应用！")
        print("📋 修复内容:")
        print("  ✅ 选择项目页面现在会显示所有推荐项目")
        print("  ✅ 按类型分组显示（冲刺/匹配/保底）")
        print("  ✅ 显示匹配度和项目详情")
        print("  ✅ 添加了选择统计和导航按钮")
        print()
        print("🌐 请在浏览器中刷新页面: http://localhost:8501")
        print("📱 或者按 Ctrl+F5 强制刷新")
        
    except Exception as e:
        print(f"❌ 连接应用失败: {e}")
        print("💡 请手动刷新浏览器页面")

if __name__ == "__main__":
    reload_streamlit_app()
