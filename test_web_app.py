"""
测试Web应用是否正常运行
"""
import requests
import time
import sys

def test_web_app():
    """测试Web应用"""
    url = "http://localhost:8501"
    
    print("🧪 测试留学申请匹配Agent Web应用...")
    print(f"📍 测试URL: {url}")
    
    # 等待应用启动
    print("⏳ 等待应用启动...")
    time.sleep(3)
    
    try:
        # 测试应用是否响应
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✅ Web应用启动成功！")
            print(f"📊 响应状态码: {response.status_code}")
            print(f"🌐 应用地址: {url}")
            print()
            print("🎉 您现在可以在浏览器中访问应用了！")
            print()
            print("📋 应用功能:")
            print("  1. 📝 填写基本信息（姓名、院校、专业、GPA等）")
            print("  2. 🎯 设置留学偏好（目标国家、专业）")
            print("  3. 🎓 获取20所大学的智能推荐")
            print("  4. 📊 查看详细的匹配分析")
            print("  5. 📝 生成个性化申请文书")
            print()
            print("💡 提示: 应用已在浏览器中自动打开")
            return True
        else:
            print(f"❌ 应用响应异常，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到应用，请检查应用是否正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 连接超时，应用可能还在启动中")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def show_app_info():
    """显示应用信息"""
    print("🎓 留学申请匹配Agent - Web应用信息")
    print("=" * 50)
    print()
    print("📊 数据基础:")
    print("  • 100所QS排名大学")
    print("  • 13,902个硕士专业")
    print("  • 2,000条申请历史数据")
    print("  • 支持美加英澳新等9个国家地区")
    print()
    print("🎯 核心功能:")
    print("  • 智能背景分析")
    print("  • 科学匹配算法（9维度评分）")
    print("  • 三档推荐分类（冲刺/匹配/保底）")
    print("  • 个性化文书生成")
    print("  • 聊天式交互界面")
    print()
    print("🚀 技术特色:")
    print("  • 基于LangGraph的Agent架构")
    print("  • Streamlit响应式Web界面")
    print("  • 模块化设计，易于扩展")
    print("  • 数据驱动的推荐算法")
    print()

if __name__ == "__main__":
    show_app_info()
    success = test_web_app()
    
    if success:
        print("🎉 Web应用测试通过！")
        sys.exit(0)
    else:
        print("❌ Web应用测试失败")
        sys.exit(1)
