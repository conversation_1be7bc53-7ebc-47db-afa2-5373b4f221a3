"""
启动脚本
"""
import subprocess
import sys
import os
from pathlib import Path

def check_requirements():
    """检查依赖是否安装"""
    try:
        import streamlit
        import langchain
        import langgraph
        import pandas
        import numpy
        import faiss
        import PIL
        import cv2
        import sklearn
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False

def install_requirements():
    """安装依赖"""
    print("📦 正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def check_env_file():
    """检查环境变量文件"""
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  未找到.env文件，请创建并设置OPENAI_API_KEY")
        print("可以复制.env.example文件并修改其中的API密钥")
        return False
    
    # 检查是否设置了API密钥
    with open(env_file, 'r') as f:
        content = f.read()
        if "your_openai_api_key_here" in content:
            print("⚠️  请在.env文件中设置正确的OPENAI_API_KEY")
            return False
    
    print("✅ 环境变量配置正确")
    return True

def check_data_files():
    """检查数据文件"""
    required_files = [
        "universities.csv",
        "college.csv", 
        "department.csv",
        "degree_programs.csv",
        "courses.csv",
        "course_selection.csv",
        "graduate_application.csv"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少数据文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 数据文件完整")
    return True

def main():
    """主函数"""
    print("🎓 留学申请匹配Agent 启动检查")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        print("正在安装依赖...")
        if not install_requirements():
            print("❌ 启动失败：无法安装依赖")
            return
    
    # 检查环境变量
    if not check_env_file():
        print("❌ 启动失败：环境变量配置错误")
        return
    
    # 检查数据文件
    if not check_data_files():
        print("❌ 启动失败：数据文件不完整")
        return
    
    print("\n🚀 启动Streamlit应用...")
    print("=" * 50)
    
    # 启动Streamlit应用
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
